<?php
$pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

$message = "";

// Traitement validation/refus (à améliorer en sécurisant)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['demande_id'], $_POST['action'])) {
    $statut = null;
    if ($_POST['action'] === 'valide') {
        $statut = 'valide';
    } elseif ($_POST['action'] === 'refuse') {
        $statut = 'refuse';
    }
    if ($statut) {
        $stmt = $pdo->prepare("UPDATE demande_parrainage SET statut = ? WHERE id = ?");
        $stmt->execute([$statut, $_POST['demande_id']]);
        $message = "Statut mis à jour avec succès.";
    }
}

// Récupérer les demandes en attente
$demandes = $pdo->query("
    SELECT dp.id, p.nom AS nom_parrain, p.prenom AS prenom_parrain, p.email, dp.statut
    FROM demande_parrainage dp
    JOIN parrain p ON dp.parrain_id = p.id
    WHERE dp.statut = 'en_attente'
")->fetchAll(PDO::FETCH_ASSOC);

?>

<h2>Gestion des Parrainages</h2>

<?php if($message): ?>
  <p style="color:green;"><?= htmlspecialchars($message) ?></p>
<?php endif; ?>

<?php if (empty($demandes)): ?>
  <p>Aucune demande en attente.</p>
<?php else: ?>
  <table border="1" cellpadding="8" cellspacing="0" style="border-collapse: collapse; background: white; max-width: 800px;">
    <thead>
      <tr style="background: #007bff; color:white;">
        <th>Parrain</th>
        <th>Email</th>
        <th>Statut</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <?php foreach ($demandes as $d): ?>
        <tr>
          <td><?= htmlspecialchars($d['prenom_parrain'] . ' ' . $d['nom_parrain']) ?></td>
          <td><?= htmlspecialchars($d['email']) ?></td>
          <td><?= htmlspecialchars($d['statut']) ?></td>
          <td>
            <form method="POST" style="display:inline;">
              <input type="hidden" name="demande_id" value="<?= $d['id'] ?>">
              <button type="submit" name="action" value="valide" style="background: #28a745; color:white; border:none; padding:5px 10px;">
                Valider 
              </button>
            </form>
            <form method="POST" style="display:inline;">
              <input type="hidden" name="demande_id" value="<?= $d['id'] ?>">
              <button type="submit" name="action" value="refuse" style="background: #dc3545; color:white; border:none; padding:5px 10px;">
                Refuser
              </button>
            </form>
          </td>
        </tr>
      <?php endforeach; ?>
    </tbody>
  </table>
<?php endif; ?>
