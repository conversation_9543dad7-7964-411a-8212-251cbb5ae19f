<?php
// Connexion à la base de données avec PDO
try {
    $pdo = new PDO("mysql:host=localhost;dbname=gestion_enfant", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Récupération des données pour les centres d'accueil
    $nombre_enfants = $pdo->query("SELECT COUNT(*) as total FROM enfant WHERE structure_id IN (SELECT id FROM structure WHERE type_structure_id = 4)")->fetchColumn();
    $nombre_projets_actifs = $pdo->query("SELECT COUNT(*) as total FROM projet WHERE statut_projet_id = 2 AND structure_id IN (SELECT id FROM structure WHERE type_structure_id = 4)")->fetchColumn();
    $nombre_demandes_en_attente = $pdo->query("SELECT COUNT(*) as total FROM demande_parrainage WHERE statut = 'en_attente' AND structure_id IN (SELECT id FROM structure WHERE type_structure_id = 4)")->fetchColumn();
    $nombre_centres = $pdo->query("SELECT COUNT(*) as total FROM structure WHERE type_structure_id = 4")->fetchColumn(); // Centres d'accueil
} catch (PDOException $e) {
    echo "Erreur de connexion : " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Admin - Centres d'Accueil</title>
    <style>
        /* Ajoutez vos styles ici... */

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f2f4f8;
            color: #2c3e50;
        }

        header {
            background-color: #1e3d59;
            color: white;
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        header h1 {
            font-size: 24px;
        }

        .btn-logout {
            background-color: #e74c3c;
            border: none;
            color: white;
            padding: 10px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.3s;
        }

        .btn-logout:hover {
            background-color: #c0392b;
        }

        .container {
            padding: 40px 30px;
            margin-left: 220px; /* Pour laisser de l'espace pour la sidebar */
        }

        .title-section {
            text-align: center;
            margin-bottom: 40px;
            font-size: 26px;
            font-weight: 600;
        }

        .grid-cards {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
        }

        .card {
            background-color: white;
            width: 300px;
            border-radius: 12px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
            padding: 25px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h2 {
            color: #1e3d59;
            margin-bottom: 15px;
        }

        .card p {
            font-size: 14px;
            color: #555;
            margin-bottom: 20px;
        }

        .sidebar {
            width: 200px;
            background-color: #1e3d59;
            position: fixed;
            height: 100%;
            padding: 20px;
        }

        .sidebar ul {
            list-style-type: none;
            padding: 0;
        }

        .sidebar ul li {
            margin: 15px 0;
        }

        .sidebar ul li a {
            color: white;
            text-decoration: none;
            font-weight: 600;
        }

        .sidebar ul li a:hover {
            color: #e74c3c;
        }

        @media screen and (max-width: 600px) {
            .grid-cards {
                flex-direction: column;
                align-items: center;
            }

            .card {
                width: 90%;
            }

            .container {
                margin-left: 0; /* Enlever le margin-left pour mobile */
            }
        }

        
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f2f4f8;
            color: #2c3e50;
        }

        .grid-cards {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
        }

        .card {
            background-color: white;
            width: 250px; /* Largeur réduite */
            border-radius: 12px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
            padding: 25px;
            text-align: center;
            transition: transform 0.3s ease;
            cursor: pointer; /* Changement du curseur pour indiquer que c'est cliquable */
        }

        .card:hover {
            transform: translateY(-5px);
            background-color: #eaeaea; /* Changement de couleur au survol */
        }

        /* ... autres styles ... */
    </style>
</head>
<body>
    <?php include 'header_centre.php'; ?>
    <div class="container">
        <h2 class="title-section">Statistiques des Centres d'Accueil</h2>
        <div class="grid-cards">
            <div class="card" onclick="window.location.href='gestion_centres.php';">
                <h2>Centres Enregistrés</h2>
                <p><?php echo $nombre_centres; ?></p>
            </div>
            <div class="card">
                <h2>Enfants dans les centres</h2>
                <p><?php echo $nombre_enfants; ?></p>
            </div>
            <div class="card">
                <h2>Projets Actifs</h2>
                <p><?php echo $nombre_projets_actifs; ?></p>
            </div>
            <div class="card">
                <h2>Demandes en attente</h2>
                <p><?php echo $nombre_demandes_en_attente; ?></p>
            </div>
        </div>
    </div>
</body>
</html>