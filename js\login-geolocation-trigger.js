/**
 * Déclencheur de Géolocalisation Post-Connexion - Umwana Voice
 * Détecte automatiquement les connexions et déclenche la géolocalisation
 */

class LoginGeolocationTrigger {
    constructor() {
        this.isActive = false;
        this.checkInterval = null;
        this.maxWaitTime = 30000; // 30 secondes max d'attente
        this.startTime = Date.now();
        
        console.log('🔐 LoginGeolocationTrigger initialisé');
        this.init();
    }

    /**
     * Initialisation du système de détection
     */
    init() {
        // Vérifier si on vient de se connecter
        this.checkForRecentLogin();
        
        // Surveiller les changements de page
        this.setupPageChangeDetection();
        
        // Surveiller les changements de session
        this.setupSessionMonitoring();
    }

    /**
     * Vérifier si l'utilisateur vient de se connecter
     */
    checkForRecentLogin() {
        const justLoggedIn = sessionStorage.getItem('umwana_just_logged_in');
        const loginTimestamp = sessionStorage.getItem('umwana_login_timestamp');
        
        if (justLoggedIn === 'true' && loginTimestamp) {
            const timeSinceLogin = Date.now() - parseInt(loginTimestamp);
            
            // Si la connexion est récente (moins de 10 secondes)
            if (timeSinceLogin < 10000) {
                console.log('🎯 Connexion récente détectée - déclenchement géolocalisation...');
                this.triggerPostLoginGeolocation();
            }
        }
    }

    /**
     * Déclencher la géolocalisation après connexion
     */
    async triggerPostLoginGeolocation() {
        if (this.isActive) return;
        this.isActive = true;
        
        try {
            console.log('🚀 Déclenchement géolocalisation post-connexion...');
            
            // Attendre que les scripts soient chargés
            await this.waitForAutoLocationManager();
            
            // Déclencher la géolocalisation
            if (window.autoLocationManager) {
                await window.autoLocationManager.triggerLoginDetection();
                
                // Afficher une notification de bienvenue avec géolocalisation
                this.showWelcomeWithLocation();
                
                console.log('✅ Géolocalisation post-connexion déclenchée');
            }
            
            // Nettoyer les marqueurs de connexion
            this.cleanupLoginMarkers();
            
        } catch (error) {
            console.warn('⚠️ Erreur géolocalisation post-connexion:', error);
        } finally {
            this.isActive = false;
        }
    }

    /**
     * Attendre que AutoLocationManager soit disponible
     */
    async waitForAutoLocationManager() {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            const checkAvailability = () => {
                if (window.autoLocationManager && window.autoLocationManager.isInitialized) {
                    resolve();
                } else if (Date.now() - startTime > this.maxWaitTime) {
                    reject(new Error('Timeout waiting for AutoLocationManager'));
                } else {
                    setTimeout(checkAvailability, 100);
                }
            };
            
            checkAvailability();
        });
    }

    /**
     * Configurer la détection des changements de page
     */
    setupPageChangeDetection() {
        // Détecter les redirections après connexion
        const originalLocation = window.location.href;
        
        // Surveiller les changements d'URL
        const checkUrlChange = () => {
            if (window.location.href !== originalLocation) {
                console.log('🔄 Changement de page détecté après connexion');
                
                // Vérifier si on est sur une page qui nécessite la géolocalisation
                if (this.isGeolocationPage()) {
                    setTimeout(() => this.checkForRecentLogin(), 500);
                }
            }
        };
        
        // Vérifier périodiquement les changements d'URL
        setInterval(checkUrlChange, 1000);
        
        // Écouter les événements de navigation
        window.addEventListener('popstate', () => {
            setTimeout(() => this.checkForRecentLogin(), 500);
        });
    }

    /**
     * Vérifier si la page actuelle nécessite la géolocalisation
     */
    isGeolocationPage() {
        const path = window.location.pathname.toLowerCase();
        const geolocationPages = [
            'dashboard',
            'home1',
            'gestion',
            'centre',
            'orphelinat',
            'structure'
        ];
        
        return geolocationPages.some(page => path.includes(page));
    }

    /**
     * Configurer la surveillance de session
     */
    setupSessionMonitoring() {
        // Surveiller les changements dans sessionStorage
        const originalSetItem = sessionStorage.setItem;
        
        sessionStorage.setItem = function(key, value) {
            originalSetItem.apply(this, arguments);
            
            // Si une nouvelle session est créée
            if (key.includes('user') || key.includes('session')) {
                console.log('📝 Nouvelle session détectée');
                setTimeout(() => {
                    if (window.loginGeolocationTrigger) {
                        window.loginGeolocationTrigger.checkForRecentLogin();
                    }
                }, 1000);
            }
        };
    }

    /**
     * Afficher une notification de bienvenue avec géolocalisation
     */
    showWelcomeWithLocation() {
        // Créer une notification de bienvenue
        const notification = document.createElement('div');
        notification.className = 'welcome-notification';
        notification.innerHTML = `
            <div class="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-blue-600 text-white px-6 py-3 rounded-lg shadow-lg flex items-center space-x-3">
                <span class="text-xl">👋</span>
                <div>
                    <div class="font-medium">Bienvenue !</div>
                    <div class="text-sm opacity-90">Détection automatique de votre position en cours...</div>
                </div>
                <div class="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Supprimer après 5 secondes
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.transition = 'opacity 0.3s ease-out';
                notification.style.opacity = '0';
                setTimeout(() => notification.remove(), 300);
            }
        }, 5000);
    }

    /**
     * Nettoyer les marqueurs de connexion
     */
    cleanupLoginMarkers() {
        // Nettoyer après un délai pour éviter les déclenchements multiples
        setTimeout(() => {
            sessionStorage.removeItem('umwana_just_logged_in');
            sessionStorage.removeItem('umwana_login_timestamp');
            console.log('🧹 Marqueurs de connexion nettoyés');
        }, 5000);
    }

    /**
     * Forcer le déclenchement (pour tests)
     */
    forceTriggering() {
        console.log('🔧 Déclenchement forcé de la géolocalisation post-connexion');
        sessionStorage.setItem('umwana_just_logged_in', 'true');
        sessionStorage.setItem('umwana_login_timestamp', Date.now().toString());
        this.triggerPostLoginGeolocation();
    }

    /**
     * Désactiver le système
     */
    disable() {
        this.isActive = false;
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
        }
        console.log('⏹️ LoginGeolocationTrigger désactivé');
    }
}

// Styles CSS pour les notifications
const styles = `
<style>
.welcome-notification {
    animation: slideInFromTop 0.3s ease-out;
}

@keyframes slideInFromTop {
    from {
        transform: translate(-50%, -100%);
        opacity: 0;
    }
    to {
        transform: translate(-50%, 0);
        opacity: 1;
    }
}

.welcome-notification .animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>
`;

// Injecter les styles
document.head.insertAdjacentHTML('beforeend', styles);

// Initialisation automatique
const loginGeolocationTrigger = new LoginGeolocationTrigger();

// Export global pour accès externe
window.loginGeolocationTrigger = loginGeolocationTrigger;

// Fonction utilitaire pour déclencher manuellement
window.triggerLoginGeolocation = () => {
    loginGeolocationTrigger.forceTriggering();
};

console.log('✅ LoginGeolocationTrigger prêt');
