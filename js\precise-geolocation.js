/**
 * Système de Géolocalisation Haute Précision pour Umwana Voice
 * Détection précise des positions utilisateurs et centres au Burundi
 */

class PreciseGeolocation {
    constructor() {
        this.currentPosition = null;
        this.watchId = null;
        this.accuracyThreshold = 50; // Précision souhaitée en mètres
        this.maxAttempts = 5;
        this.attempts = 0;
        this.callbacks = {
            onSuccess: null,
            onError: null,
            onProgress: null
        };
        
        // Limites géographiques du Burundi
        this.burundiLimits = {
            latMin: -4.5,
            latMax: -2.3,
            lngMin: 28.9,
            lngMax: 30.9
        };
    }

    /**
     * Obtenir la position avec haute précision
     */
    async getPreciseLocation(callbacks = {}) {
        this.callbacks = { ...this.callbacks, ...callbacks };
        
        if (!navigator.geolocation) {
            this.handleError({
                code: 'NOT_SUPPORTED',
                message: 'La géolocalisation n\'est pas supportée par ce navigateur'
            });
            return;
        }

        this.attempts = 0;
        this.startPreciseLocationDetection();
    }

    /**
     * Démarrer la détection précise avec plusieurs tentatives
     */
    startPreciseLocationDetection() {
        this.attempts++;
        
        if (this.callbacks.onProgress) {
            this.callbacks.onProgress({
                attempt: this.attempts,
                maxAttempts: this.maxAttempts,
                message: `Tentative ${this.attempts}/${this.maxAttempts} - Recherche de position précise...`
            });
        }

        const options = this.getLocationOptions();
        
        navigator.geolocation.getCurrentPosition(
            (position) => this.handleLocationSuccess(position),
            (error) => this.handleLocationError(error),
            options
        );
    }

    /**
     * Options de géolocalisation progressives
     */
    getLocationOptions() {
        const baseOptions = {
            enableHighAccuracy: true,
            maximumAge: 30000
        };

        // Options progressives selon le nombre de tentatives
        switch (this.attempts) {
            case 1:
                return { ...baseOptions, timeout: 20000 };
            case 2:
                return { ...baseOptions, timeout: 15000, maximumAge: 60000 };
            case 3:
                return { ...baseOptions, timeout: 10000, enableHighAccuracy: false };
            default:
                return { ...baseOptions, timeout: 5000, enableHighAccuracy: false, maximumAge: 300000 };
        }
    }

    /**
     * Gérer le succès de localisation
     */
    handleLocationSuccess(position) {
        const { latitude, longitude, accuracy, altitude, heading, speed } = position.coords;
        
        // Vérifier si la position est dans les limites du Burundi
        if (!this.isValidBurundiCoordinate(latitude, longitude)) {
            if (this.attempts < this.maxAttempts) {
                setTimeout(() => this.startPreciseLocationDetection(), 2000);
                return;
            } else {
                this.handleError({
                    code: 'OUTSIDE_BURUNDI',
                    message: 'Position détectée en dehors du Burundi. Veuillez vérifier vos paramètres GPS.'
                });
                return;
            }
        }

        // Vérifier la précision
        if (accuracy > this.accuracyThreshold && this.attempts < this.maxAttempts) {
            if (this.callbacks.onProgress) {
                this.callbacks.onProgress({
                    attempt: this.attempts,
                    maxAttempts: this.maxAttempts,
                    accuracy: accuracy,
                    message: `Précision: ${accuracy.toFixed(0)}m - Amélioration en cours...`
                });
            }
            
            setTimeout(() => this.startPreciseLocationDetection(), 2000);
            return;
        }

        // Position acceptée
        this.currentPosition = {
            latitude,
            longitude,
            accuracy,
            altitude,
            heading,
            speed,
            timestamp: position.timestamp,
            attempts: this.attempts
        };

        if (this.callbacks.onSuccess) {
            this.callbacks.onSuccess(this.currentPosition);
        }
    }

    /**
     * Gérer les erreurs de localisation
     */
    handleLocationError(error) {
        if (this.attempts < this.maxAttempts) {
            setTimeout(() => this.startPreciseLocationDetection(), 3000);
            return;
        }

        let errorInfo = {
            code: error.code,
            message: this.getErrorMessage(error.code)
        };

        this.handleError(errorInfo);
    }

    /**
     * Messages d'erreur personnalisés
     */
    getErrorMessage(code) {
        switch (code) {
            case 1:
                return 'Permission de géolocalisation refusée. Veuillez autoriser l\'accès à votre position.';
            case 2:
                return 'Position non disponible. Vérifiez que votre GPS est activé.';
            case 3:
                return 'Délai de géolocalisation expiré. Réessayez dans un endroit avec meilleur signal.';
            default:
                return 'Erreur de géolocalisation inconnue.';
        }
    }

    /**
     * Vérifier si les coordonnées sont valides pour le Burundi
     */
    isValidBurundiCoordinate(lat, lng) {
        return lat >= this.burundiLimits.latMin && 
               lat <= this.burundiLimits.latMax && 
               lng >= this.burundiLimits.lngMin && 
               lng <= this.burundiLimits.lngMax;
    }

    /**
     * Corriger automatiquement les coordonnées invalides
     */
    correctInvalidCoordinates(lat, lng) {
        // Si les coordonnées sont complètement invalides, utiliser Bujumbura comme défaut
        if (Math.abs(lat) > 90 || Math.abs(lng) > 180) {
            return {
                latitude: -3.3614,
                longitude: 29.3599,
                corrected: true,
                reason: 'Coordonnées invalides - Position par défaut (Bujumbura)'
            };
        }

        // Si les coordonnées sont en dehors du Burundi mais valides
        if (!this.isValidBurundiCoordinate(lat, lng)) {
            return {
                latitude: -3.3614,
                longitude: 29.3599,
                corrected: true,
                reason: 'Position en dehors du Burundi - Position par défaut (Bujumbura)'
            };
        }

        return {
            latitude: lat,
            longitude: lng,
            corrected: false
        };
    }

    /**
     * Surveiller la position en continu
     */
    watchPosition(callbacks = {}) {
        this.callbacks = { ...this.callbacks, ...callbacks };
        
        if (this.watchId) {
            navigator.geolocation.clearWatch(this.watchId);
        }

        const options = {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 30000
        };

        this.watchId = navigator.geolocation.watchPosition(
            (position) => this.handleLocationSuccess(position),
            (error) => this.handleLocationError(error),
            options
        );
    }

    /**
     * Arrêter la surveillance
     */
    stopWatching() {
        if (this.watchId) {
            navigator.geolocation.clearWatch(this.watchId);
            this.watchId = null;
        }
    }

    /**
     * Calculer la distance entre deux points
     */
    calculateDistance(lat1, lng1, lat2, lng2) {
        const R = 6371; // Rayon de la Terre en km
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLng = (lng2 - lng1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                  Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }

    /**
     * Gérer les erreurs finales
     */
    handleError(errorInfo) {
        if (this.callbacks.onError) {
            this.callbacks.onError(errorInfo);
        }
    }

    /**
     * Obtenir la position actuelle
     */
    getCurrentPosition() {
        return this.currentPosition;
    }

    /**
     * Vérifier si une position est disponible
     */
    hasPosition() {
        return this.currentPosition !== null;
    }
}

// Export pour utilisation globale
window.PreciseGeolocation = PreciseGeolocation;
