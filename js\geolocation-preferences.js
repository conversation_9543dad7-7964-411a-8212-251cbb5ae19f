/**
 * Système de Préférences Géolocalisation - Umwana Voice
 * Interface utilisateur pour contrôler tous les automatismes
 */

class GeolocationPreferences {
    constructor() {
        this.preferences = this.loadPreferences();
        this.isModalOpen = false;
        
        console.log('⚙️ GeolocationPreferences initialisé');
        this.init();
    }

    /**
     * Initialisation du système
     */
    init() {
        this.createPreferencesButton();
        this.createPreferencesModal();
        this.applyPreferences();
        
        // Écouter les changements de préférences
        window.addEventListener('geolocationPreferencesChanged', (event) => {
            this.handlePreferencesChange(event.detail);
        });
    }

    /**
     * Charger les préférences sauvegardées
     */
    loadPreferences() {
        try {
            const saved = localStorage.getItem('umwana_geolocation_preferences');
            if (saved) {
                return { ...this.getDefaultPreferences(), ...JSON.parse(saved) };
            }
        } catch (error) {
            console.warn('Erreur chargement préférences:', error);
        }
        
        return this.getDefaultPreferences();
    }

    /**
     * Préférences par défaut
     */
    getDefaultPreferences() {
        return {
            enableAutoDetection: true,           // Auto-détection au chargement
            enableLoginDetection: true,          // Auto-détection à la connexion
            enableContinuousTracking: true,      // Surveillance continue
            enableBatteryOptimization: true,     // Optimisation batterie
            enableLocationMemory: true,          // Mémorisation position
            enableSmartFallbacks: true,          // Fallbacks intelligents
            enableDetailedProgress: true,        // Affichage progrès détaillé
            enableNotifications: true,           // Notifications
            trackingFrequency: 'balanced',       // 'performance', 'balanced', 'economy'
            privacyLevel: 'standard',           // 'minimal', 'standard', 'detailed'
            autoStartDelay: 1000,               // Délai avant auto-start (ms)
            showTrackingIndicator: true         // Afficher indicateur de suivi
        };
    }

    /**
     * Sauvegarder les préférences
     */
    savePreferences() {
        try {
            localStorage.setItem('umwana_geolocation_preferences', JSON.stringify(this.preferences));
            console.log('💾 Préférences sauvegardées');
        } catch (error) {
            console.error('Erreur sauvegarde préférences:', error);
        }
    }

    /**
     * Créer le bouton d'accès aux préférences
     */
    createPreferencesButton() {
        const button = document.createElement('div');
        button.id = 'geolocation-preferences-button';
        button.innerHTML = `
            <button onclick="window.geolocationPreferences.openModal()" 
                    class="fixed bottom-4 left-4 bg-gray-600 hover:bg-gray-700 text-white p-3 rounded-full shadow-lg z-40 transition-all duration-200"
                    title="Paramètres de géolocalisation">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
            </button>
        `;
        
        document.body.appendChild(button);
    }

    /**
     * Créer la modal des préférences
     */
    createPreferencesModal() {
        const modal = document.createElement('div');
        modal.id = 'geolocation-preferences-modal';
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50';
        
        modal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                            <span class="text-2xl mr-2">📍</span>
                            Paramètres de Géolocalisation
                        </h2>
                        <button onclick="window.geolocationPreferences.closeModal()" 
                                class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    
                    <div class="space-y-6">
                        <!-- Automatismes de base -->
                        <div class="border-b pb-4">
                            <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">🚀 Automatismes</h3>
                            <div class="space-y-3">
                                ${this.createToggle('enableAutoDetection', 'Auto-détection au chargement', 'Démarrer automatiquement la géolocalisation quand vous ouvrez une page')}
                                ${this.createToggle('enableLoginDetection', 'Auto-détection à la connexion', 'Démarrer automatiquement après votre connexion')}
                                ${this.createToggle('enableContinuousTracking', 'Surveillance continue', 'Suivre votre position en temps réel')}
                                ${this.createToggle('enableSmartFallbacks', 'Fallbacks intelligents', 'Proposer des alternatives si la géolocalisation échoue')}
                            </div>
                        </div>
                        
                        <!-- Optimisations -->
                        <div class="border-b pb-4">
                            <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">⚡ Optimisations</h3>
                            <div class="space-y-3">
                                ${this.createToggle('enableBatteryOptimization', 'Optimisation batterie', 'Adapter la fréquence selon le niveau de batterie')}
                                ${this.createToggle('enableLocationMemory', 'Mémorisation position', 'Sauvegarder votre dernière position connue')}
                                ${this.createSelect('trackingFrequency', 'Fréquence de suivi', {
                                    'performance': 'Performance (1 min)',
                                    'balanced': 'Équilibré (2 min)',
                                    'economy': 'Économie (5 min)'
                                })}
                            </div>
                        </div>
                        
                        <!-- Interface -->
                        <div class="border-b pb-4">
                            <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">🎨 Interface</h3>
                            <div class="space-y-3">
                                ${this.createToggle('enableDetailedProgress', 'Progrès détaillé', 'Afficher les détails du processus de géolocalisation')}
                                ${this.createToggle('enableNotifications', 'Notifications', 'Recevoir des notifications sur l\'état de la géolocalisation')}
                                ${this.createToggle('showTrackingIndicator', 'Indicateur de suivi', 'Afficher un indicateur quand le suivi est actif')}
                            </div>
                        </div>
                        
                        <!-- Confidentialité -->
                        <div class="pb-4">
                            <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">🔒 Confidentialité</h3>
                            <div class="space-y-3">
                                ${this.createSelect('privacyLevel', 'Niveau de confidentialité', {
                                    'minimal': 'Minimal (position uniquement)',
                                    'standard': 'Standard (position + précision)',
                                    'detailed': 'Détaillé (toutes les données)'
                                })}
                                ${this.createRange('autoStartDelay', 'Délai auto-démarrage', 0, 5000, 500, 'ms')}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Actions -->
                    <div class="flex justify-between items-center mt-6 pt-4 border-t">
                        <button onclick="window.geolocationPreferences.resetToDefaults()" 
                                class="px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200">
                            Réinitialiser
                        </button>
                        <div class="space-x-3">
                            <button onclick="window.geolocationPreferences.closeModal()" 
                                    class="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-lg">
                                Annuler
                            </button>
                            <button onclick="window.geolocationPreferences.saveAndApply()" 
                                    class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg">
                                Sauvegarder
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }

    /**
     * Créer un toggle switch
     */
    createToggle(key, label, description) {
        const checked = this.preferences[key] ? 'checked' : '';
        return `
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">${label}</label>
                    <p class="text-xs text-gray-500 dark:text-gray-400">${description}</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer ml-4">
                    <input type="checkbox" ${checked} class="sr-only peer" onchange="window.geolocationPreferences.updatePreference('${key}', this.checked)">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
            </div>
        `;
    }

    /**
     * Créer un select
     */
    createSelect(key, label, options) {
        const optionsHtml = Object.entries(options).map(([value, text]) => 
            `<option value="${value}" ${this.preferences[key] === value ? 'selected' : ''}>${text}</option>`
        ).join('');
        
        return `
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">${label}</label>
                <select onchange="window.geolocationPreferences.updatePreference('${key}', this.value)" 
                        class="w-full p-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white">
                    ${optionsHtml}
                </select>
            </div>
        `;
    }

    /**
     * Créer un range slider
     */
    createRange(key, label, min, max, step, unit) {
        return `
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    ${label}: <span id="${key}-value">${this.preferences[key]}${unit}</span>
                </label>
                <input type="range" min="${min}" max="${max}" step="${step}" value="${this.preferences[key]}"
                       onchange="window.geolocationPreferences.updatePreference('${key}', parseInt(this.value)); document.getElementById('${key}-value').textContent = this.value + '${unit}'"
                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
            </div>
        `;
    }

    /**
     * Mettre à jour une préférence
     */
    updatePreference(key, value) {
        this.preferences[key] = value;
        console.log(`⚙️ Préférence mise à jour: ${key} = ${value}`);
    }

    /**
     * Ouvrir la modal
     */
    openModal() {
        const modal = document.getElementById('geolocation-preferences-modal');
        modal.classList.remove('hidden');
        modal.classList.add('flex');
        this.isModalOpen = true;
    }

    /**
     * Fermer la modal
     */
    closeModal() {
        const modal = document.getElementById('geolocation-preferences-modal');
        modal.classList.add('hidden');
        modal.classList.remove('flex');
        this.isModalOpen = false;
    }

    /**
     * Sauvegarder et appliquer les préférences
     */
    saveAndApply() {
        this.savePreferences();
        this.applyPreferences();
        this.closeModal();
        
        // Notifier les autres composants
        window.dispatchEvent(new CustomEvent('geolocationPreferencesChanged', {
            detail: this.preferences
        }));
        
        // Afficher confirmation
        if (typeof showCustomAlert === 'function') {
            showCustomAlert('Préférences', 'Paramètres sauvegardés et appliqués', 'success');
        }
    }

    /**
     * Réinitialiser aux valeurs par défaut
     */
    resetToDefaults() {
        if (confirm('Êtes-vous sûr de vouloir réinitialiser tous les paramètres ?')) {
            this.preferences = this.getDefaultPreferences();
            this.closeModal();
            this.createPreferencesModal(); // Recréer avec les nouvelles valeurs
            this.openModal();
        }
    }

    /**
     * Appliquer les préférences aux systèmes
     */
    applyPreferences() {
        console.log('🔧 Application des préférences:', this.preferences);
        
        // Appliquer à AutoLocationManager
        if (window.autoLocationManager) {
            window.autoLocationManager.updateSettings({
                enableAutoDetection: this.preferences.enableAutoDetection,
                enableContinuousTracking: this.preferences.enableContinuousTracking,
                enableSmartFallbacks: this.preferences.enableSmartFallbacks,
                enableLocationMemory: this.preferences.enableLocationMemory,
                showDetailedProgress: this.preferences.enableDetailedProgress,
                enableLoginDetection: this.preferences.enableLoginDetection,
                enableBatteryOptimization: this.preferences.enableBatteryOptimization
            });
        }
        
        // Appliquer à ContinuousTracking
        if (window.continuousTracking) {
            const frequencyMap = {
                'performance': 60000,   // 1 minute
                'balanced': 120000,     // 2 minutes
                'economy': 300000       // 5 minutes
            };
            
            window.continuousTracking.adaptiveSettings.interval = frequencyMap[this.preferences.trackingFrequency];
        }
    }

    /**
     * Obtenir les préférences actuelles
     */
    getPreferences() {
        return { ...this.preferences };
    }
}

// Instance globale
window.geolocationPreferences = new GeolocationPreferences();

console.log('✅ GeolocationPreferences prêt');
