<?php
// Connexion PDO (adapter vos identifiants)
$pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');

// Traitement de la soumission
$message = "";
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['nom'], $_POST['adresse'])) {
    $stmt = $pdo->prepare("INSERT INTO structure (nom, adresse, type_structure_id) VALUES (?, ?, 1)");
    $stmt->execute([$_POST['nom'], $_POST['adresse']]);
    $message = "Centre enregistré avec succès !";
}

// Récupérer la liste des centres
$centres = $pdo->query("SELECT id, nom, adresse FROM structure WHERE type_structure_id = 1")->fetchAll(PDO::FETCH_ASSOC);
?>

<h2>Gestion des Centres d'encadrement</h2>

<?php if ($message): ?>
  <p style="color:green;"><?= htmlspecialchars($message) ?></p>
<?php endif; ?>

<form method="POST">
  <label for="nom">Nom du centre</label>
  <input type="text" name="nom" id="nom" required>

  <label for="adresse">Adresse</label>
  <textarea name="adresse" id="adresse" rows="3" required></textarea>

  <button type="submit">Ajouter un centre</button>
</form>

<hr>

<h3>Liste des Centres</h3>
<ul>
  <?php foreach ($centres as $c): ?>
    <li><?= htmlspecialchars($c['nom']) ?> — <?= htmlspecialchars($c['adresse']) ?></li>
  <?php endforeach; ?>
</ul>
