/**
 * Système de Géolocalisation Intelligente - Umwana Voice
 * Détection automatique avec respect des permissions et fallbacks
 */

class SmartGeolocation {
    constructor() {
        this.currentPosition = null;
        this.watchId = null;
        this.permissionStatus = null;
        this.autoDetectionEnabled = true;
        this.callbacks = {
            onSuccess: null,
            onError: null,
            onProgress: null,
            onPermissionChange: null
        };
        
        // Configuration adaptative - AUTOMATISATION COMPLÈTE
        this.config = {
            enableAutoDetection: true,           // ✅ Auto-détection activée
            enableContinuousTracking: true,      // ✅ ACTIVÉ: Surveillance continue
            accuracyThreshold: 50,               // Seuil de précision
            maxAttempts: 5,                      // Tentatives maximum
            fallbackToManual: true,              // Fallback manuel
            respectUserPreferences: true,        // Respect préférences utilisateur
            trackingInterval: 60000,             // ✅ NOUVEAU: Intervalle tracking (1 min)
            enableHighAccuracy: true,            // ✅ NOUVEAU: Haute précision par défaut
            enableBackgroundTracking: true,      // ✅ NOUVEAU: Tracking en arrière-plan
            enableSmartPowerManagement: true     // ✅ NOUVEAU: Gestion intelligente énergie
        };
        
        // Limites géographiques du Burundi
        this.burundiLimits = {
            latMin: -4.5,
            latMax: -2.3,
            lngMin: 28.9,
            lngMax: 30.9
        };
        
        this.init();
    }

    /**
     * Initialisation intelligente du système
     */
    async init() {
        // Vérifier le support de géolocalisation
        if (!navigator.geolocation) {
            this.handleError({
                code: 'NOT_SUPPORTED',
                message: 'Géolocalisation non supportée par ce navigateur'
            });
            return;
        }

        // Vérifier les permissions existantes
        await this.checkPermissions();
        
        // Vérifier les préférences utilisateur stockées
        this.loadUserPreferences();
        
        // AUTOMATISATION COMPLÈTE - Démarrer immédiatement si possible
        if (this.config.enableAutoDetection) {
            if (this.permissionStatus === 'granted') {
                console.log('🚀 Permissions accordées - démarrage immédiat');
                this.startAutoDetection();

                // NOUVEAU: Démarrer surveillance continue immédiatement
                if (this.config.enableContinuousTracking) {
                    setTimeout(() => this.startContinuousTracking(), 2000);
                }
            } else if (this.permissionStatus === 'prompt') {
                // Permission pas encore demandée - démarrage intelligent
                console.log('🎯 Préparation démarrage intelligent...');
                this.prepareSmartDetection();

                // NOUVEAU: Tentative automatique après 1 seconde
                setTimeout(() => this.attemptAutoStart(), 1000);
            }
        }
    }

    /**
     * Vérifier les permissions de géolocalisation
     */
    async checkPermissions() {
        try {
            if ('permissions' in navigator) {
                const permission = await navigator.permissions.query({ name: 'geolocation' });
                this.permissionStatus = permission.state;
                
                // Écouter les changements de permission
                permission.addEventListener('change', () => {
                    this.permissionStatus = permission.state;
                    this.handlePermissionChange(permission.state);
                });
                
                console.log(`Permission géolocalisation: ${permission.state}`);
            } else {
                // Fallback pour navigateurs sans API permissions
                this.permissionStatus = 'prompt';
            }
        } catch (error) {
            console.warn('Impossible de vérifier les permissions:', error);
            this.permissionStatus = 'prompt';
        }
    }

    /**
     * Charger les préférences utilisateur
     */
    loadUserPreferences() {
        try {
            const prefs = localStorage.getItem('umwana_geolocation_prefs');
            if (prefs) {
                const preferences = JSON.parse(prefs);
                this.config = { ...this.config, ...preferences };
                console.log('Préférences utilisateur chargées:', this.config);
            }
        } catch (error) {
            console.warn('Erreur chargement préférences:', error);
        }
    }

    /**
     * Sauvegarder les préférences utilisateur
     */
    saveUserPreferences() {
        try {
            localStorage.setItem('umwana_geolocation_prefs', JSON.stringify(this.config));
        } catch (error) {
            console.warn('Erreur sauvegarde préférences:', error);
        }
    }

    /**
     * Démarrer la détection automatique
     */
    startAutoDetection() {
        if (!this.config.enableAutoDetection) return;
        
        console.log('🚀 Démarrage détection automatique...');
        
        if (this.callbacks.onProgress) {
            this.callbacks.onProgress({
                type: 'auto_detection',
                message: 'Détection automatique de votre position...'
            });
        }

        this.getPreciseLocation({
            silent: true, // Mode silencieux pour auto-détection
            autoRetry: true
        });
    }

    /**
     * Préparer la détection intelligente
     */
    prepareSmartDetection() {
        console.log('🎯 Préparation détection intelligente...');
        
        // Détecter l'interaction utilisateur pour déclencher la géolocalisation
        this.setupSmartTriggers();
    }

    /**
     * Configurer les déclencheurs intelligents
     */
    setupSmartTriggers() {
        // Déclenchement sur première interaction utilisateur
        const triggerEvents = ['click', 'touch', 'keydown'];
        
        const smartTrigger = () => {
            if (this.permissionStatus === 'prompt' && this.config.enableAutoDetection) {
                console.log('🎯 Déclenchement intelligent de la géolocalisation');
                this.requestLocationSmart();
            }
            
            // Supprimer les listeners après première utilisation
            triggerEvents.forEach(event => {
                document.removeEventListener(event, smartTrigger);
            });
        };
        
        // Ajouter les listeners
        triggerEvents.forEach(event => {
            document.addEventListener(event, smartTrigger, { once: true });
        });
    }

    /**
     * Demander la localisation de manière intelligente
     */
    async requestLocationSmart() {
        try {
            // Tentative de géolocalisation avec gestion d'erreur gracieuse
            await this.getPreciseLocation({
                showProgress: true,
                autoFallback: true
            });
        } catch (error) {
            console.log('Géolocalisation automatique échouée, mode manuel disponible');
            if (this.callbacks.onError) {
                this.callbacks.onError({
                    code: 'AUTO_FAILED',
                    message: 'Détection automatique échouée - sélection manuelle disponible',
                    canFallback: true
                });
            }
        }
    }

    /**
     * Obtenir la position avec haute précision
     */
    async getPreciseLocation(options = {}) {
        const {
            silent = false,
            showProgress = true,
            autoRetry = true,
            autoFallback = false
        } = options;

        return new Promise((resolve, reject) => {
            if (!silent && showProgress && this.callbacks.onProgress) {
                this.callbacks.onProgress({
                    type: 'detection',
                    message: 'Recherche de votre position précise...'
                });
            }

            const geoOptions = {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 30000
            };

            navigator.geolocation.getCurrentPosition(
                (position) => {
                    this.handleLocationSuccess(position, { silent, resolve });
                },
                (error) => {
                    this.handleLocationError(error, { 
                        silent, 
                        autoRetry, 
                        autoFallback, 
                        reject 
                    });
                },
                geoOptions
            );
        });
    }

    /**
     * Gérer le succès de localisation
     */
    handleLocationSuccess(position, options = {}) {
        const { silent = false, resolve } = options;
        const { latitude, longitude, accuracy } = position.coords;
        
        // Vérifier si la position est valide pour le Burundi
        if (!this.isValidBurundiCoordinate(latitude, longitude)) {
            if (!silent) {
                console.warn('Position en dehors du Burundi détectée');
            }
            // Continuer quand même mais avec un avertissement
        }

        this.currentPosition = {
            latitude,
            longitude,
            accuracy,
            timestamp: position.timestamp,
            method: 'gps'
        };

        if (!silent && this.callbacks.onSuccess) {
            this.callbacks.onSuccess(this.currentPosition);
        }

        if (resolve) {
            resolve(this.currentPosition);
        }

        // Démarrer la surveillance continue si activée
        if (this.config.enableContinuousTracking) {
            this.startContinuousTracking();
        }

        console.log('✅ Position détectée:', this.currentPosition);
    }

    /**
     * Gérer les erreurs de localisation
     */
    handleLocationError(error, options = {}) {
        const { silent = false, autoRetry = false, autoFallback = false, reject } = options;
        
        let errorInfo = {
            code: error.code,
            message: this.getErrorMessage(error.code),
            canRetry: autoRetry,
            canFallback: autoFallback
        };

        if (!silent) {
            console.warn('Erreur géolocalisation:', errorInfo);
            
            if (this.callbacks.onError) {
                this.callbacks.onError(errorInfo);
            }
        }

        if (reject) {
            reject(errorInfo);
        }
    }

    /**
     * Démarrer la surveillance continue
     */
    startContinuousTracking() {
        if (this.watchId) {
            navigator.geolocation.clearWatch(this.watchId);
        }

        console.log('🔄 Démarrage surveillance continue...');

        const options = {
            enableHighAccuracy: true,
            timeout: 15000,
            maximumAge: 60000
        };

        this.watchId = navigator.geolocation.watchPosition(
            (position) => {
                this.handleLocationSuccess(position, { silent: true });
                
                // Notifier les changements de position
                if (this.callbacks.onProgress) {
                    this.callbacks.onProgress({
                        type: 'position_update',
                        message: 'Position mise à jour',
                        position: this.currentPosition
                    });
                }
            },
            (error) => {
                console.warn('Erreur surveillance continue:', error);
            },
            options
        );
    }

    /**
     * Arrêter la surveillance continue
     */
    stopContinuousTracking() {
        if (this.watchId) {
            navigator.geolocation.clearWatch(this.watchId);
            this.watchId = null;
            console.log('⏹️ Surveillance continue arrêtée');
        }
    }

    /**
     * Gérer les changements de permission
     */
    handlePermissionChange(newState) {
        console.log(`🔄 Permission changée: ${this.permissionStatus} → ${newState}`);
        
        if (newState === 'granted' && this.config.enableAutoDetection) {
            // Permission accordée - démarrer la détection
            this.startAutoDetection();
        } else if (newState === 'denied') {
            // Permission refusée - arrêter la surveillance
            this.stopContinuousTracking();
        }

        if (this.callbacks.onPermissionChange) {
            this.callbacks.onPermissionChange(newState);
        }
    }

    /**
     * Configurer les callbacks
     */
    setCallbacks(callbacks) {
        this.callbacks = { ...this.callbacks, ...callbacks };
    }

    /**
     * Mettre à jour la configuration
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.saveUserPreferences();
        
        // Appliquer les nouveaux paramètres
        if (newConfig.enableContinuousTracking && !this.watchId) {
            this.startContinuousTracking();
        } else if (!newConfig.enableContinuousTracking && this.watchId) {
            this.stopContinuousTracking();
        }
    }

    /**
     * Vérifier si les coordonnées sont valides pour le Burundi
     */
    isValidBurundiCoordinate(lat, lng) {
        return lat >= this.burundiLimits.latMin && 
               lat <= this.burundiLimits.latMax && 
               lng >= this.burundiLimits.lngMin && 
               lng <= this.burundiLimits.lngMax;
    }

    /**
     * Obtenir le message d'erreur
     */
    getErrorMessage(code) {
        switch (code) {
            case 1:
                return 'Permission de géolocalisation refusée';
            case 2:
                return 'Position non disponible';
            case 3:
                return 'Délai de géolocalisation expiré';
            default:
                return 'Erreur de géolocalisation inconnue';
        }
    }

    /**
     * Obtenir la position actuelle
     */
    getCurrentPosition() {
        return this.currentPosition;
    }

    /**
     * Vérifier si une position est disponible
     */
    hasPosition() {
        return this.currentPosition !== null;
    }

    /**
     * NOUVEAU: Tentative de démarrage automatique
     */
    async attemptAutoStart() {
        try {
            console.log('🎯 Tentative démarrage automatique...');

            // Essayer de démarrer sans interaction utilisateur
            await this.getPreciseLocation({
                silent: true,
                showProgress: false,
                autoRetry: false
            });

            console.log('✅ Démarrage automatique réussi');

            // Démarrer surveillance continue si activée
            if (this.config.enableContinuousTracking) {
                setTimeout(() => this.startContinuousTracking(), 1000);
            }

        } catch (error) {
            console.log('⚠️ Démarrage automatique échoué - attente interaction utilisateur');
            // Continuer avec la détection intelligente normale
        }
    }

    /**
     * NOUVEAU: Surveillance continue optimisée
     */
    startContinuousTracking() {
        if (this.watchId) {
            navigator.geolocation.clearWatch(this.watchId);
        }

        console.log('🔄 Démarrage surveillance continue optimisée...');

        // Configuration adaptative selon les capacités
        const options = {
            enableHighAccuracy: this.config.enableHighAccuracy,
            timeout: this.config.enableSmartPowerManagement ? 20000 : 15000,
            maximumAge: this.config.trackingInterval / 2 // Cache pendant la moitié de l'intervalle
        };

        this.watchId = navigator.geolocation.watchPosition(
            (position) => {
                this.handleLocationSuccess(position, { silent: true, fromTracking: true });

                // Notifier les changements de position significatifs seulement
                if (this.isSignificantPositionChange(position)) {
                    if (this.callbacks.onProgress) {
                        this.callbacks.onProgress({
                            type: 'position_update',
                            message: 'Position mise à jour automatiquement',
                            position: this.currentPosition,
                            isAutomatic: true
                        });
                    }
                }
            },
            (error) => {
                console.warn('Erreur surveillance continue:', error);

                // Retry intelligent en cas d'erreur
                if (this.config.enableSmartPowerManagement) {
                    setTimeout(() => {
                        if (!this.watchId) { // Seulement si pas déjà redémarré
                            this.startContinuousTracking();
                        }
                    }, 30000); // Retry après 30 secondes
                }
            },
            options
        );

        // NOUVEAU: Gestion intelligente de l'énergie
        if (this.config.enableSmartPowerManagement) {
            this.setupPowerManagement();
        }
    }

    /**
     * NOUVEAU: Vérifier si le changement de position est significatif
     */
    isSignificantPositionChange(newPosition) {
        if (!this.currentPosition) return true;

        const distance = this.calculateDistance(
            this.currentPosition.latitude,
            this.currentPosition.longitude,
            newPosition.coords.latitude,
            newPosition.coords.longitude
        );

        // Considérer significatif si > 10 mètres
        return distance > 0.01; // ~10 mètres
    }

    /**
     * NOUVEAU: Calculer distance entre deux points
     */
    calculateDistance(lat1, lng1, lat2, lng2) {
        const R = 6371; // Rayon de la Terre en km
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLng = (lng2 - lng1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                  Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }

    /**
     * NOUVEAU: Configuration gestion intelligente de l'énergie
     */
    setupPowerManagement() {
        // Réduire la fréquence si l'utilisateur est stationnaire
        let stationaryCount = 0;
        const originalInterval = this.config.trackingInterval;

        const checkStationaryStatus = () => {
            if (this.currentPosition) {
                // Si pas de mouvement significatif, réduire la fréquence
                stationaryCount++;

                if (stationaryCount > 3) { // 3 positions identiques
                    console.log('📍 Utilisateur stationnaire - réduction fréquence');
                    this.config.trackingInterval = originalInterval * 3; // 3x moins fréquent
                    this.restartTracking();
                }
            }
        };

        // Vérifier toutes les 5 minutes
        setInterval(checkStationaryStatus, 300000);

        // Réinitialiser si mouvement détecté
        if (this.callbacks.onProgress) {
            const originalCallback = this.callbacks.onProgress;
            this.callbacks.onProgress = (progress) => {
                if (progress.type === 'position_update') {
                    stationaryCount = 0; // Reset si mouvement
                    this.config.trackingInterval = originalInterval; // Restaurer fréquence normale
                }
                originalCallback(progress);
            };
        }
    }

    /**
     * NOUVEAU: Redémarrer le tracking avec nouveaux paramètres
     */
    restartTracking() {
        if (this.watchId) {
            this.stopContinuousTracking();
            setTimeout(() => this.startContinuousTracking(), 1000);
        }
    }

    /**
     * Nettoyer les ressources
     */
    destroy() {
        this.stopContinuousTracking();
        this.currentPosition = null;
        this.callbacks = {};
    }
}

// Export pour utilisation globale
window.SmartGeolocation = SmartGeolocation;
