<?php
require_once 'config.php';

try {
    $stats = [];
    
    // Nombre total de structures actives
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM structure WHERE active = 1");
    $stats['total_structures'] = $stmt->fetch()['total'];
    
    // Capacité totale
    $stmt = $pdo->query("SELECT SUM(capacite_max) as total FROM structure WHERE active = 1");
    $stats['total_capacity'] = $stmt->fetch()['total'] ?: 0;
    
    // Capacité utilisée
    $stmt = $pdo->query("SELECT SUM(capacite_actuelle) as total FROM structure WHERE active = 1");
    $stats['used_capacity'] = $stmt->fetch()['total'] ?: 0;
    
    // Structures par type
    $stmt = $pdo->query("
        SELECT ts.nom, COUNT(*) as count 
        FROM structure s 
        JOIN type_structure ts ON s.type_structure_id = ts.id 
        WHERE s.active = 1 
        GROUP BY ts.nom
    ");
    $stats['by_type'] = $stmt->fetchAll();
    
    // Structures récentes (derniers 30 jours)
    $stmt = $pdo->query("
        SELECT COUNT(*) as total 
        FROM structure 
        WHERE active = 1 AND date_creation >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    ");
    $stats['recent_structures'] = $stmt->fetch()['total'];
    
    echo json_encode([
        'success' => true,
        'statistics' => $stats
    ]);
    
} catch(Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Erreur lors de la récupération des statistiques: ' . $e->getMessage()
    ]);
}
?>