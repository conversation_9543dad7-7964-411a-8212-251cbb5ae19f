<?php
require_once 'config.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Méthode non autorisée'
    ]);
    exit;
}

try {
    // Validation des données requises
    $required_fields = ['nom', 'type_structure_id', 'adresse', 'telephone', 'responsable', 'latitude', 'longitude', 'capacite_max'];
    
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            echo json_encode([
                'success' => false,
                'message' => "Le champ '$field' est requis"
            ]);
            exit;
        }
    }
    
    // Validation des coordonnées GPS
    $latitude = (float) $_POST['latitude'];
    $longitude = (float) $_POST['longitude'];
    
    if ($latitude < -90 || $latitude > 90 || $longitude < -180 || $longitude > 180) {
        echo json_encode([
            'success' => false,
            'message' => 'Coordonnées GPS invalides'
        ]);
        exit;
    }
    
    // Validation du type de structure
    $type_check = $pdo->prepare("SELECT id FROM type_structure WHERE id = ?");
    $type_check->execute([$_POST['type_structure_id']]);
    
    if (!$type_check->fetch()) {
        echo json_encode([
            'success' => false,
            'message' => 'Type de structure invalide'
        ]);
        exit;
    }
    
    // Insertion de la structure
    $sql = "
        INSERT INTO structure (
            nom, 
            adresse, 
            latitude, 
            longitude, 
            capacite_max, 
            capacite_actuelle, 
            telephone, 
            email, 
            responsable, 
            type_structure_id, 
            date_creation, 
            active
        ) VALUES (
            :nom, 
            :adresse, 
            :latitude, 
            :longitude, 
            :capacite_max, 
            :capacite_actuelle, 
            :telephone, 
            :email, 
            :responsable, 
            :type_structure_id, 
            NOW(), 
            1
        )
    ";
    
    $stmt = $pdo->prepare($sql);
    
    $result = $stmt->execute([
        ':nom' => trim($_POST['nom']),
        ':adresse' => trim($_POST['adresse']),
        ':latitude' => $latitude,
        ':longitude' => $longitude,
        ':capacite_max' => (int) $_POST['capacite_max'],
        ':capacite_actuelle' => isset($_POST['capacite_actuelle']) ? (int) $_POST['capacite_actuelle'] : 0,
        ':telephone' => trim($_POST['telephone']),
        ':email' => isset($_POST['email']) ? trim($_POST['email']) : null,
        ':responsable' => trim($_POST['responsable']),
        ':type_structure_id' => (int) $_POST['type_structure_id']
    ]);
    
    if ($result) {
        $structure_id = $pdo->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'message' => 'Structure ajoutée avec succès',
            'structure_id' => $structure_id
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Erreur lors de l\'ajout de la structure'
        ]);
    }
    
} catch(Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Erreur: ' . $e->getMessage()
    ]);
}
?>