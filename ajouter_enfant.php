<?php
// Connexion à la base de données
$pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');

// Récupération des listes de sélection
$sexes = $pdo->query("SELECT id, nom FROM sexe")->fetchAll(PDO::FETCH_ASSOC);
$statuts = $pdo->query("SELECT id, nom FROM statut_enfant")->fetchAll(PDO::FETCH_ASSOC);
$structures = $pdo->query("SELECT id, nom FROM structure WHERE type_structure_id = 4")->fetchAll(PDO::FETCH_ASSOC);

// Traitement du formulaire
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $nom = $_POST['nom'];
    $prenom = $_POST['prenom'];
    $date_naissance = $_POST['datenaissance'];
    $historique = $_POST['historique_accueil'];
    $handicap = isset($_POST['handicap']) ? 1 : 0;
    $date_enregistrement = date('Y-m-d');
    $date_maj = $date_enregistrement;
    $sexe_id = $_POST['sexe_id'];
    $statut_enfant_id = $_POST['statut_enfant_id'];
    $structure_id = $_POST['structure_id'];

    // Fichiers : photo_portrait, photo, empreinte
    $photo_portrait = $photo = $empreinte = null;

    function uploadFile($inputName) {
        if (isset($_FILES[$inputName]) && $_FILES[$inputName]['error'] === 0) {
            $fileName = uniqid() . '_' . basename($_FILES[$inputName]['name']);
            $path = 'uploads/' . $fileName;
            move_uploaded_file($_FILES[$inputName]['tmp_name'], $path);
            return $path;
        }
        return null;
    }

    $photo_portrait = uploadFile('photo_portrait');

    // Génération du matricule
    $annee_courte = date('y');
    $stmt = $pdo->prepare("SELECT matricule FROM enfant WHERE YEAR(date_enregistrement) = YEAR(NOW()) ORDER BY id DESC LIMIT 1");
    $stmt->execute();
    $lastMatricule = $stmt->fetchColumn();

    $compteur = $lastMatricule ? (int)substr($lastMatricule, strpos($lastMatricule, '/') + 1) + 1 : 1;
    $matricule = "M{$annee_courte}/" . str_pad($compteur, 6, "0", STR_PAD_LEFT);

    // Insertion en base
    $sql = "INSERT INTO enfant (matricule, nom, prenom, date_naissance, historique_accueil, date_enregistrement, derniere_mise_a_jour, sexe_id, statut_enfant_id, photo_portrait, structure_id, handicap) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$matricule, $nom, $prenom, $date_naissance, $historique, $date_enregistrement, $date_maj, $sexe_id, $statut_enfant_id, $photo_portrait, $structure_id, $handicap]);

    echo "<div class='alert alert-success'>✅ Enfant enregistré avec succès. Matricule : <strong>$matricule</strong></div>";
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Ajout d'un Enfant</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <?php include 'header_centre.php'; ?>
<div class="container mt-2">
    <h2>Formulaire d'enregistrement d'un enfant</h2><br>
    <form method="post" enctype="multipart/form-data" class="row g-2 bg-white p-10 shadow rounded">
        <div class="col-md-5">
            <label class="form-label">Nom</label>
            <input type="text" name="nom" class="form-control" required>
        </div>
        <div class="col-md-6">
            <label class="form-label">Prénom</label>
            <input type="text" name="prenom" class="form-control" required>
        </div>

        <div class="col-md-4">
            <label class="form-label">Date de naissance</label>
            <input type="date" name="datenaissance" class="form-control" required>
        </div>

        <div class="col-md-4">
            <label class="form-label">Sexe</label>
            <select name="sexe_id" class="form-select" required>
                <option value="">Choisir...</option>
                <?php foreach ($sexes as $s): ?>
                    <option value="<?= $s['id'] ?>"><?= $s['nom'] ?></option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="col-md-4">
            <label class="form-label">Statut de l'enfant</label>
            <select name="statut_enfant_id" class="form-select" required>
                <option value="">Choisir...</option>
                <?php foreach ($statuts as $st): ?>
                    <option value="<?= $st['id'] ?>"><?= $st['nom'] ?></option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="col-md-6">
            <label class="form-label">Historique d'accueil</label>
            <select name="historique_accueil" class="form-select" required>
                <option value="">Choisir...</option>
                <option value="malade">Malade</option>
                <option value="handicap">Handicap</option>
                <option value="malade mentale">Malade mentale</option>
                <option value="bonne sante">Bonne santé</option>
            </select>
        </div>

        <div class="col-md-3">
            <label class="form-label">Structure d'accueil</label>
            <select name="structure_id" class="form-select" required>
                <option value="">Choisir...</option>
                <?php foreach ($structures as $str): ?>
                    <option value="<?= $str['id'] ?>"><?= $str['nom'] ?></option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="col-md-3">
            <label class="form-label">Handicap</label><br>
            <input type="checkbox" name="handicap" value="1"> Oui
        </div>

        <div class="col-md-4">
            <label class="form-label">Photo Portrait</label>
            <input type="file" name="photo_portrait" class="form-control" accept="image/*">
        </div>

        <div class="col-12 text-end">
            <button type="submit" class="btn btn-primary">Enregistrer</button>
        </div>
    </form>
</div>
</body>
</html>
