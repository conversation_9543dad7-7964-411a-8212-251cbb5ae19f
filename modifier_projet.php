<?php
// Connexion à la base de données
$pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');

// Vérification de l'ID du projet
if (!isset($_GET['id'])) {
    die("ID de projet manquant.");
}

$id = $_GET['id'];

// Récupération du projet
$stmt = $pdo->prepare("SELECT * FROM projet WHERE id = ?");
$stmt->execute([$id]);
$projet = $stmt->fetch(PDO::FETCH_ASSOC);

// Récupération des structures de type "centre d'accueil"
$structures = $pdo->query("SELECT id, nom FROM structure WHERE type_structure_id = 4")->fetchAll(PDO::FETCH_ASSOC);

// Traitement du formulaire
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $titre = $_POST['titre'];
    $description = $_POST['description'];
    $objectif_financier = $_POST['objectif_financier'];
    $date_debut = $_POST['date_debut'];
    $date_fin = $_POST['date_fin'];
    $structure_id = $_POST['structure_id'];

    // Mise à jour en base
    $sql = "UPDATE projet SET titre = ?, description = ?, objectif_financier = ?, date_debut = ?, date_fin = ?, structure_id = ? WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$titre, $description, $objectif_financier, $date_debut, $date_fin, $structure_id, $id]);

    echo "<div class='alert'>✅ Projet modifié avec succès.</div>";
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Modifier un Projet</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f2f2f2;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h2 {
            text-align: center;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
        }
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            transition: border 0.3s;
        }
        .form-group input:focus,
        .form-group select:focus {
            border-color: #007BFF;
        }
        .btn {
            padding: 10px 15px;
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            display: block;
            width: 100%;
        }
        .btn:hover {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <?php include 'header_centre.php'; ?>
    <div class="container">
        <h2>Modifier un Projet</h2>
        <form method="post">
            <div class="form-group">
                <label>Titre</label>
                <input type="text" name="titre" value="<?= htmlspecialchars($projet['titre']) ?>" required>
            </div>
            <div class="form-group">
                <label>Description</label>
                <input type="text" name="description" value="<?= htmlspecialchars($projet['description']) ?>" required>
            </div>
            <div class="form-group">
                <label>Objectif Financier</label>
                <input type="number" name="objectif_financier" value="<?= htmlspecialchars($projet['objectif_financier']) ?>" required>
            </div>
            <div class="form-group">
                <label>Date de Début</label>
                <input type="date" name="date_debut" value="<?= htmlspecialchars($projet['date_debut']) ?>" required>
            </div>
            <div class="form-group">
                <label>Date de Fin</label>
                <input type="date" name="date_fin" value="<?= htmlspecialchars($projet['date_fin']) ?>" required>
            </div>
            <div class="form-group">
                <label>Structure d'accueil</label>
                <select name="structure_id" required>
                    <option value="">Choisir...</option>
                    <?php foreach ($structures as $str): ?>
                        <option value="<?= $str['id'] ?>" <?= $str['id'] == $projet['structure_id'] ? 'selected' : '' ?>><?= $str['nom'] ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <button type="submit" class="btn">Modifier</button>
        </form>
    </div>
</body>
</html>