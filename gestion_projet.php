<?php
// Connexion à la base de données
$pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');

// Récupération des projets
$stmt = $pdo->prepare("
    SELECT p.*, s.nom AS structure_nom 
    FROM projet p 
    JOIN structure s ON p.structure_id = s.id 
    WHERE s.type_structure_id = 4
");
$stmt->execute();
$projets = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Gestion des Projets</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f2f2f2;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        h2 {
            text-align: center;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
        }
        table, th, td {
            border: 1px solid #ccc;
        }
        th, td {
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f4f4f4;
        }
        .btn {
            padding: 10px 15px;
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .container {
            max-width: 800px;
            margin: auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <?php include 'header_centre.php'; ?>
    <div class="container">
        <h2>Liste des Projets</h2>
        <button class="btn" onclick="window.location.href='ajouter_projet.php'">Ajouter un Projet</button>
        <table>
            <thead>
                <tr>
                    <th>Titre</th>
                    <th>Description</th>
                    <th>Structure</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($projets as $projet): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($projet['titre']); ?></td>
                        <td><?php echo htmlspecialchars($projet['description']); ?></td>
                        <td><?php echo htmlspecialchars($projet['structure_nom']); ?></td>
                        <td>
                            <a class="btn" href="modifier_projet.php?id=<?php echo $projet['id']; ?>">Modifier</a>
                            <a class="btn btn-danger" href="supprimer_projet.php?id=<?php echo $projet['id']; ?>">Supprimer</a>
                            <a class="btn" href="voir_projet.php?id=<?php echo $projet['id']; ?>">Voir Plus</a>
                        </td>
                    </tr>
                <?php endforeach; ?>
                <?php if (empty($projets)): ?>
                    <tr>
                        <td colspan="4">Aucun projet trouvé.</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</body>
</html>