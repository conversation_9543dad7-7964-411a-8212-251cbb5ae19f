<?php
require_once 'config.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Méthode non autorisée'
    ]);
    exit;
}

try {
    if (!isset($_POST['id']) || !is_numeric($_POST['id'])) {
        echo json_encode([
            'success' => false,
            'message' => 'ID de structure requis'
        ]);
        exit;
    }
    
    $structure_id = (int) $_POST['id'];
    
    // Vérifier que la structure existe
    $check_stmt = $pdo->prepare("SELECT id FROM structure WHERE id = ?");
    $check_stmt->execute([$structure_id]);
    
    if (!$check_stmt->fetch()) {
        echo json_encode([
            'success' => false,
            'message' => 'Structure non trouvée'
        ]);
        exit;
    }
    
    // Construire la requête de mise à jour dynamiquement
    $update_fields = [];
    $params = [':id' => $structure_id];
    
    $allowed_fields = ['nom', 'adresse', 'latitude', 'longitude', 'capacite_max', 'capacite_actuelle', 'telephone', 'email', 'responsable', 'type_structure_id'];
    
    foreach ($allowed_fields as $field) {
        if (isset($_POST[$field])) {
            $update_fields[] = "$field = :$field";
            $params[":$field"] = $_POST[$field];
        }
    }
    
    if (empty($update_fields)) {
        echo json_encode([
            'success' => false,
            'message' => 'Aucun champ à mettre à jour'
        ]);
        exit;
    }
    
    $sql = "UPDATE structure SET " . implode(', ', $update_fields) . " WHERE id = :id";
    
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute($params);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Structure mise à jour avec succès'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Erreur lors de la mise à jour'
        ]);
    }
    
} catch(Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Erreur: ' . $e->getMessage()
    ]);
}
?>