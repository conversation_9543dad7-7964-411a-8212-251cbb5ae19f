<?php
// Connexion
try {
    $pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');
} catch (PDOException $e) {
    die("Erreur de connexion : " . $e->getMessage());
}

// Récupérer l'orphelinat
if (!isset($_GET['id'])) {
    header("Location: orphelinats_gestion.php");
    exit();
}
$id = intval($_GET['id']);
$stmt = $pdo->prepare("SELECT * FROM structure WHERE id = ?");
$stmt->execute([$id]);
$orphelinat = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$orphelinat) {
    die("Orphelinat introuvable.");
}

// Mise à jour
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nom = $_POST['nom'];
    $adresse = $_POST['adresse'];
    $telephone = $_POST['telephone'];
    $responsable = $_POST['responsable'];

    $stmt = $pdo->prepare("UPDATE structure SET nom=?, adresse=?, telephone=?, responsable=? WHERE id=?");
    $stmt->execute([$nom, $adresse, $telephone, $responsable, $id]);

    header("Location: orphelinats_gestion.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Modifier un Orphelinat</title>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Poppins', sans-serif;
      background-color: #f7f9fc;
      padding: 40px;
      color: #2c3e50;
    }

    h2 {
      text-align: center;
      margin-bottom: 30px;
      color: #1e3d59;
    }

    form {
      max-width: 600px;
      margin: auto;
      background-color: #fff;
      padding: 25px;
      border-radius: 10px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
    }

    input, textarea {
      width: 100%;
      padding: 10px;
      margin-bottom: 20px;
      border: 1px solid #ccc;
      border-radius: 6px;
    }

    button {
      background-color: #3498db;
      color: white;
      padding: 10px 18px;
      border: none;
      border-radius: 6px;
      font-weight: bold;
      cursor: pointer;
    }

    button:hover {
      background-color: #2980b9;
    }
  </style>
</head>
<body>

<h2>Modifier l'Orphelinat</h2>

<form method="post">
  <label for="nom">Nom</label>
  <input type="text" name="nom" id="nom" value="<?= htmlspecialchars($orphelinat['nom']) ?>" required>

  <label for="adresse">Adresse</label>
  <textarea name="adresse" id="adresse" rows="3" required><?= htmlspecialchars($orphelinat['adresse']) ?></textarea>

  <label for="telephone">Téléphone</label>
  <input type="text" name="telephone" id="telephone" value="<?= htmlspecialchars($orphelinat['telephone']) ?>">

  <label for="responsable">Responsable</label>
  <input type="text" name="responsable" id="responsable" value="<?= htmlspecialchars($orphelinat['responsable']) ?>">

  <button type="submit">Modifier</button>
</form>

</body>
</html>
