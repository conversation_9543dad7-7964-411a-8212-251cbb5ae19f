<?php
require_once 'config.php';

try {
    $sql = "SELECT id, nom FROM type_structure  ORDER BY nom";
    $stmt = $pdo->query($sql);
    $types = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'types' => $types
    ]);
    
} catch(Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Erreur lors de la récupération des types: ' . $e->getMessage()
    ]);
}
?>