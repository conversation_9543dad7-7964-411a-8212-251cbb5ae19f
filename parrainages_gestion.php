<?php
// Connexion à la base
session_start();
$pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

// Récupération des enfants parrainés
$sql = "
    SELECT p.nom AS nom_parrain, p.prenom AS prenom_parrain, e.matricule, e.nom AS nom_enfant, s.nom AS nom_structure
    FROM demande_parrainage dp
    JOIN parrain p ON dp.parrain_id = p.id
    JOIN enfant e ON dp.enfant_id = e.id
    JOIN structure s ON e.structure_id = s.id
    WHERE dp.statut = 'valide'
";
$parrainages = $pdo->query($sql)->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Accueil Parrainage - Admin</title>
    <style>
        body {
            margin: 0;
            font-family: 'Segoe UI', sans-serif;
            background-color: #f4f6f9;
        }

        header {
            background-color: #007bff;
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        header h1 {
            margin: 0;
            font-size: 24px;
        }

        nav a {
            color: white;
            text-decoration: none;
            background-color: #0056b3;
            padding: 10px 15px;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }

        nav a:hover {
            background-color: #003d80;
        }

        .container {
            padding: 30px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            background: white;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        table th, table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        table th {
            background-color: #007bff;
            color: white;
        }

        tr:hover {
            background-color: #f1f1f1;
        }

        .titre-section {
            font-size: 22px;
            margin-bottom: 10px;
            color: #333;
        }
    </style>
</head>
<body>

<header>
    <h1>🏠 Accueil du Parrainage - Administrateur</h1>
    <nav>
        <a href="gestion_demandes.php">🗂️ Gestion des Demandes</a>
    </nav>
</header>

<div class="container">
    <div class="titre-section">📋 Liste des parrains et leurs enfants parrainés</div>
    <table>
        <thead>
            <tr>
                <th>Nom du Parrain</th>
                <th>Prénom</th>
                <th>Nom de l’Enfant</th>
                <th>Matricule</th>
                <th>Structure</th>
            </tr>
        </thead>
        <tbody>
            <?php if (count($parrainages) > 0): ?>
                <?php foreach ($parrainages as $row): ?>
                    <tr>
                        <td><?= htmlspecialchars($row['nom_parrain']) ?></td>
                        <td><?= htmlspecialchars($row['prenom_parrain']) ?></td>
                        <td><?= htmlspecialchars($row['nom_enfant']) ?></td>
                        <td><?= htmlspecialchars($row['matricule']) ?></td>
                        <td><?= htmlspecialchars($row['nom_structure']) ?></td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="5">Aucun enfant parrainé pour l’instant.</td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

</body>
</html>
