<?php
/**
 * Script pour mettre à jour les coordonnées avec des positions réalistes au Burundi
 * Distribue les centres dans différentes villes du Burundi
 */

require_once 'config.php';

// Coordonnées réalistes de différentes villes du Burundi
$burundiLocations = [
    'Bujumbura_Centre' => ['lat' => -3.3614, 'lng' => 29.3599, 'name' => 'Bujumbura Centre'],
    'Bujumbura_Kamenge' => ['lat' => -3.3500, 'lng' => 29.3700, 'name' => 'Bujumbura Kamenge'],
    'Bujumbura_Nyakabiga' => ['lat' => -3.3800, 'lng' => 29.3400, 'name' => 'Bujumbura Nyakabiga'],
    'Bujumbura_Rohero' => ['lat' => -3.3400, 'lng' => 29.3500, 'name' => 'Bujumbura Rohero'],
    '<PERSON><PERSON><PERSON><PERSON><PERSON>_Kanyosha' => ['lat' => -3.4200, 'lng' => 29.3300, 'name' => 'Bujumbura Kanyosha'],
    '<PERSON><PERSON>ga' => ['lat' => -3.4264, 'lng' => 29.9306, 'name' => 'Gitega'],
    'Muyinga' => ['lat' => -2.8442, 'lng' => 30.3436, 'name' => 'Muyinga'],
    'Ngozi' => ['lat' => -2.9075, 'lng' => 29.8306, 'name' => 'Ngozi'],
    'Ruyigi' => ['lat' => -3.4764, 'lng' => 30.2500, 'name' => 'Ruyigi'],
    'Kayanza' => ['lat' => -2.9222, 'lng' => 29.6292, 'name' => 'Kayanza'],
    'Kirundo' => ['lat' => -2.5847, 'lng' => 30.0944, 'name' => 'Kirundo'],
    'Bururi' => ['lat' => -3.9489, 'lng' => 29.6244, 'name' => 'Bururi'],
    'Makamba' => ['lat' => -4.1378, 'lng' => 29.8044, 'name' => 'Makamba'],
    'Rumonge' => ['lat' => -3.9733, 'lng' => 29.4386, 'name' => 'Rumonge']
];

try {
    // Récupérer toutes les structures
    $sql = "SELECT id, nom, adresse FROM structure WHERE active = 1";
    $stmt = $pdo->query($sql);
    $structures = $stmt->fetchAll();
    
    $updated = 0;
    $updates = [];
    $locationKeys = array_keys($burundiLocations);
    
    foreach ($structures as $index => $structure) {
        // Sélectionner une location basée sur l'index pour une distribution équitable
        $locationKey = $locationKeys[$index % count($locationKeys)];
        $location = $burundiLocations[$locationKey];
        
        // Ajouter une petite variation aléatoire pour éviter que tous les centres soient exactement au même endroit
        $latVariation = (rand(-100, 100) / 10000); // ±0.01 degré (environ ±1km)
        $lngVariation = (rand(-100, 100) / 10000);
        
        $newLat = $location['lat'] + $latVariation;
        $newLng = $location['lng'] + $lngVariation;
        
        // Mettre à jour la structure
        $updateSql = "UPDATE structure SET latitude = ?, longitude = ? WHERE id = ?";
        $updateStmt = $pdo->prepare($updateSql);
        $updateStmt->execute([$newLat, $newLng, $structure['id']]);
        
        $updates[] = [
            'id' => $structure['id'],
            'nom' => $structure['nom'],
            'adresse' => $structure['adresse'],
            'new_location' => $location['name'],
            'coordinates' => ['lat' => $newLat, 'lng' => $newLng],
            'variation' => ['lat' => $latVariation, 'lng' => $lngVariation]
        ];
        
        $updated++;
    }
    
    // Retourner le résultat
    echo json_encode([
        'success' => true,
        'message' => "Coordonnées mises à jour: $updated structures distribuées dans " . count($burundiLocations) . " villes du Burundi",
        'updated_count' => $updated,
        'locations_used' => count($burundiLocations),
        'updates' => $updates
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Erreur lors de la mise à jour: ' . $e->getMessage()
    ]);
}

/**
 * Fonction pour valider les coordonnées du Burundi
 */
function isValidBurundiCoordinate($lat, $lng) {
    return $lat >= -4.5 && 
           $lat <= -2.3 && 
           $lng >= 28.9 && 
           $lng <= 30.9;
}

/**
 * Fonction pour calculer la distance entre deux points
 */
function calculateDistance($lat1, $lng1, $lat2, $lng2) {
    $R = 6371; // Rayon de la Terre en km
    $dLat = deg2rad($lat2 - $lat1);
    $dLng = deg2rad($lng2 - $lng1);
    $a = sin($dLat/2) * sin($dLat/2) +
         cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
         sin($dLng/2) * sin($dLng/2);
    $c = 2 * atan2(sqrt($a), sqrt(1-a));
    return $R * $c;
}
?>
