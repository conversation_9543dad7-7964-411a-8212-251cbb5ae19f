<?php
$pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');
// Récupérer les centres
$centres = $pdo->query("SELECT id, nom FROM structure WHERE type_structure_id = 1")->fetchAll(PDO::FETCH_ASSOC);
$message = "";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['titre'], $_POST['description'], $_POST['centre_id'])) {
    $stmt = $pdo->prepare("INSERT INTO projet (titre, description, structure_id) VALUES (?, ?, ?)");
    $stmt->execute([$_POST['titre'], $_POST['description'], $_POST['centre_id']]);
    $message = "Projet ajouté avec succès !";
}
?>

<h2>Gestion des Projets</h2>

<?php if ($message): ?>
  <p style="color: green;"><?= htmlspecialchars($message) ?></p>
<?php endif; ?>

<form method="POST">
  <label for="titre">Titre du projet</label>
  <input type="text" name="titre" id="titre" required>

  <label for="description">Description</label>
  <textarea name="description" id="description" rows="4" required></textarea>

  <label for="centre_id">Centre associé</label>
  <select name="centre_id" id="centre_id" required>
    <option value="">-- Choisir un centre --</option>
    <?php foreach ($centres as $c): ?>
      <option value="<?= $c['id'] ?>"><?= htmlspecialchars($c['nom']) ?></option>
    <?php endforeach; ?>
  </select>

  <button type="submit">ajouter_projet</button>

</form>
