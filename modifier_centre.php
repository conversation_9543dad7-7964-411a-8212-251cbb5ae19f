<?php
// Connexion à la base de données avec PDO
try {
    $pdo = new PDO("mysql:host=localhost;dbname=gestion_enfant", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $id = $_POST['id'];
        $nom = $_POST['nom'];
        $adresse = $_POST['adresse'];
        $latitude = $_POST['latitude'];
        $longitude = $_POST['longitude'];
        $capacite_max = $_POST['capacite_max'];
        $capacite_actuelle = $_POST['capacite_actuelle'];
        $telephone = $_POST['telephone'];
        $email = $_POST['email'];
        $responsable = $_POST['responsable'];

        // Mise à jour dans la base de données
        $stmt = $pdo->prepare("UPDATE structure SET nom = ?, adresse = ?, latitude = ?, longitude = ?, capacite_max = ?, capacite_actuelle = ?, telephone = ?, email = ?, responsable = ? WHERE id = ?");
        $stmt->execute([$nom, $adresse, $latitude, $longitude, $capacite_max, $capacite_actuelle, $telephone, $email, $responsable, $id]);

        echo "<script>alert('Centre modifié avec succès !'); window.location.href='gestion_centres.php';</script>";
    }

    // Récupérer les données du centre
    $id = $_GET['id'];
    $stmt = $pdo->prepare("SELECT * FROM structure WHERE id = ?");
    $stmt->execute([$id]);
    $centre = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    echo "Erreur de connexion : " . $e->getMessage();
}
?>

<?php include 'header_centre.php'; ?>

<div class="container">
    <h2>Modifier Centre d'Accueil</h2>
    <form method="POST">
        <input type="hidden" name="id" value="<?php echo htmlspecialchars($centre['id']); ?>">
        <div class="form-group">
            <label for="nom">Nom du Centre:</label>
            <input type="text" id="nom" name="nom" value="<?php echo htmlspecialchars($centre['nom']); ?>" required>
        </div>
        <div class="form-group">
            <label for="adresse">Adresse:</label>
            <input type="text" id="adresse" name="adresse" value="<?php echo htmlspecialchars($centre['adresse']); ?>" required>
        </div>
        <div class="form-group">
            <label for="latitude">Latitude:</label>
            <input type="text" id="latitude" name="latitude" value="<?php echo htmlspecialchars($centre['latitude']); ?>" required>
        </div>
        <div class="form-group">
            <label for="longitude">Longitude:</label>
            <input type="text" id="longitude" name="longitude" value="<?php echo htmlspecialchars($centre['longitude']); ?>" required>
        </div>
        <div class="form-group">
            <label for="capacite_max">Capacité Max:</label>
            <input type="number" id="capacite_max" name="capacite_max" value="<?php echo htmlspecialchars($centre['capacite_max']); ?>" required>
        </div>
        <div class="form-group">
            <label for="capacite_actuelle">Capacité Actuelle:</label>
            <input type="number" id="capacite_actuelle" name="capacite_actuelle" value="<?php echo htmlspecialchars($centre['capacite_actuelle']); ?>" required>
        </div>
        <div class="form-group">
            <label for="telephone">Téléphone:</label>
            <input type="text" id="telephone" name="telephone" value="<?php echo htmlspecialchars($centre['telephone']); ?>" required>
        </div>
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($centre['email']); ?>" required>
        </div>
        <div class="form-group">
            <label for="responsable">Responsable:</label>
            <input type="text" id="responsable" name="responsable" value="<?php echo htmlspecialchars($centre['responsable']); ?>" required>
        </div>
        <button type="submit" class="btn-submit">Modifier</button>
    </form>
</div>