<?php 
// Connexion à la base de données
try {
    $pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');
} catch (PDOException $e) {
    die("Erreur de connexion : " . $e->getMessage());
}

// Suppression
if (isset($_GET['supprimer'])) {
    $id = intval($_GET['supprimer']);
    $pdo->prepare("DELETE FROM structure WHERE id = ?")->execute([$id]);
    header("Location: orphelinats_gestion.php");
    exit();
}

// Requête avec jointure pour n'afficher que les orphelinats
$sql = "SELECT s.*, ts.nom AS type_nom 
        FROM structure s
        JOIN type_structure ts ON s.type_structure_id = ts.id
        WHERE ts.nom = 'orphelinat'
        ORDER BY s.id DESC";

$orphelinats = $pdo->query($sql)->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Gestion des Orphelinats</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Poppins', sans-serif;
      background-color: #f4f6f8;
      color: #2c3e50;
      padding: 30px;
    }

    h1 {
      text-align: center;
      color: #1e3d59;
      margin-bottom: 40px;
    }

    .btn-ajouter {
      background-color: #2ecc71;
      color: white;
      padding: 10px 18px;
      border: none;
      border-radius: 6px;
      text-decoration: none;
      font-weight: bold;
      display: inline-block;
      margin-bottom: 20px;
    }

    .btn-ajouter:hover {
      background-color: #27ae60;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      background-color: white;
      box-shadow: 0 4px 10px rgba(0,0,0,0.05);
    }

    th, td {
      padding: 10px;
      border-bottom: 1px solid #ddd;
      text-align: left;
      font-size: 14px;
    }

    th {
      background-color: #1e3d59;
      color: white;
    }

    .actions a {
      padding: 6px 10px;
      border-radius: 5px;
      font-size: 14px;
      margin-right: 5px;
      color: white;
      text-decoration: none;
    }

    .btn-modifier {
      background-color: #3498db;
    }

    .btn-modifier:hover {
      background-color: #2980b9;
      margin button: 20px;
    }

    .btn-supprimer {
      background-color: #e74c3c;
    }

    .btn-supprimer:hover {
      background-color: #c0392b;
    }

    td.email {
      font-size: 13px;
      color: #555;
    }

    @media screen and (max-width: 768px) {
      table, thead, tbody, th, td, tr {
        display: block;
      }

      td, th {
        padding: 8px;
        border-bottom: 1px solid #ccc;
      }

      th {
        background-color: #1e3d59;
        color: white;
        font-weight: 600;
      }
    }
  </style>
</head>
<body>

<h1>Gestion des Orphelinats</h1>

<a href="ajouter_orphelinat.php" class="btn-ajouter">+ Ajouter un orphelinat</a>

<table>
  <thead>
    <tr>
      <th>ID</th>
      <th>Nom</th>
      <th>Adresse</th>
      <th>Longitude</th>
      <th>Latitude</th>
      <th>Capacité Max</th>
      <th>Capacité Actuelle</th>
      <th>Email</th>
      <th>Responsable</th>
      <th>Date Création</th>
      <th>Type de Structure</th>
      <th>Actions</th>
    </tr>
  </thead>
  <tbody>
    <?php foreach ($orphelinats as $orphelinat): ?>
      <tr>
        <td><?= htmlspecialchars($orphelinat['id']) ?></td>
        <td><?= htmlspecialchars($orphelinat['nom']) ?></td>
        <td><?= htmlspecialchars($orphelinat['adresse']) ?></td>
        <td><?= htmlspecialchars($orphelinat['longitude']) ?></td>
        <td><?= htmlspecialchars($orphelinat['latitude']) ?></td>
        <td><?= htmlspecialchars($orphelinat['capacite_max']) ?></td>
        <td><?= htmlspecialchars($orphelinat['capacite_actuelle']) ?></td>
        <td class="email"><?= htmlspecialchars($orphelinat['email']) ?></td>
        <td><?= htmlspecialchars($orphelinat['responsable']) ?></td>
        <td><?= htmlspecialchars($orphelinat['date_creation']) ?></td>
        <td><?= htmlspecialchars($orphelinat['type_nom']) ?></td>
        <td class="actions">
          <a href="modifier_orphelinat.php?id=<?= $orphelinat['id'] ?>" class="btn-modifier">Modifier</a>
          <a href="orphelinats_gestion.php?supprimer=<?= $orphelinat['id'] ?>" class="btn-supprimer" onclick="return confirm('Confirmer la suppression ?')">Supprimer</a>
        </td>
      </tr>
    <?php endforeach; ?>
  </tbody>
</table>

</body>
</html>
