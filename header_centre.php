<!-- header.php -->
<?php
 // Assurez-vous que les sessions sont démarrées
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Centres</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f2f4f8;
            color: #2c3e50;
        }

        header {
            background-color: #1e3d59;
            color: white;
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        header h1 {
            font-size: 24px;
        }

        .btn-logout {
            background-color: #e74c3c;
            border: none;
            color: white;
            padding: 10px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.3s;
        }

        .btn-logout:hover {
            background-color: #c0392b;
        }

        .sidebar {
            width: 200px;
            background-color: #1e3d59;
            position: fixed;
            height: 100%;
            padding: 20px;
        }

        .sidebar ul {
            list-style-type: none;
            padding: 0;
        }

        .sidebar ul li {
            margin: 15px 0;
        }

        .sidebar ul li a {
            color: white;
            text-decoration: none;
            font-weight: 600;
        }

        .sidebar ul li a:hover {
            color: #e74c3c;
        }

        .container {
            margin-left: 220px; /* Espace pour la sidebar */
            padding: 40px 30px;
        }

        .grid-cards {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
        }

        .card {
            background-color: white;
            width: 250px; /* Largeur réduite */
            border-radius: 12px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
            padding: 25px;
            text-align: center;
            transition: transform 0.3s ease;
            cursor: pointer; /* Indiquer que c'est cliquable */
        }

        .card:hover {
            transform: translateY(-5px);
            background-color: #eaeaea; /* Changement de couleur au survol */
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 6px;
        }

        .btn-submit {
            background-color: #1e3d59;
            color: white;
            padding: 10px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.3s;
        }

        .btn-submit:hover {
            background-color: #163146;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #1e3d59;
            color: white;
        }
        .form-group input,
.form-group select {
    width: 70%; /* Réduit la largeur des champs de formulaire */
    padding: 10px; /* Ajoute un peu de padding pour le confort */
    border: 1px solid #ccc; /* Bordure standard */
    border-radius: 6px; /* Coins arrondis */
    margin: 10px 0; /* Espace entre les champs */
}
    </style>
</head>
<body>
    <header>
    <h1>Dashboard Admin - Centres d'Accueil</h1>
    <a href="logout.php" class="btn-logout">Déconnexion</a>
</header>

<aside class="sidebar">
    <ul>
        <li><a href="dashboard_centre.php">Dashboard</a></li>
        <li><a href="gestion_centres.php">Gestion Centres</a></li>
        <li><a href="gestion_enfants_centre.php">Gestion des enfants</a></li>
        <li><a href="gestion_projet.php">Gestion des Projets</a></li>
        <li><a href="demandes.php">Demandes</a></li>
    </ul>
</aside>