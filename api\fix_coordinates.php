<?php
/**
 * Script de correction des coordonnées GPS invalides
 * Corrige automatiquement les coordonnées en dehors du Burundi
 */

require_once 'config.php';

// Limites géographiques du Burundi
define('BURUNDI_LAT_MIN', -4.5);
define('BURUNDI_LAT_MAX', -2.3);
define('BURUNDI_LNG_MIN', 28.9);
define('BURUNDI_LNG_MAX', 30.9);

// Coordonnées par défaut (Bujumbura centre)
define('DEFAULT_LAT', -3.3614);
define('DEFAULT_LNG', 29.3599);

// Coordonnées de villes principales du Burundi pour correction intelligente
$burundiCities = [
    'Bujumbura' => ['lat' => -3.3614, 'lng' => 29.3599],
    'Gitega' => ['lat' => -3.4264, 'lng' => 29.9306],
    'Muyinga' => ['lat' => -2.8442, 'lng' => 30.3436],
    'Ngozi' => ['lat' => -2.9075, 'lng' => 29.8306],
    '<PERSON><PERSON><PERSON>' => ['lat' => -3.4764, 'lng' => 30.2500],
    'Kayanza' => ['lat' => -2.9222, 'lng' => 29.6292],
    'Kirundo' => ['lat' => -2.5847, 'lng' => 30.0944],
    'Bururi' => ['lat' => -3.9489, 'lng' => 29.6244],
    'Makamba' => ['lat' => -4.1378, 'lng' => 29.8044],
    'Rumonge' => ['lat' => -3.9733, 'lng' => 29.4386]
];

try {
    // Récupérer toutes les structures avec coordonnées invalides
    $sql = "SELECT id, nom, adresse, latitude, longitude FROM structure WHERE active = 1";
    $stmt = $pdo->query($sql);
    $structures = $stmt->fetchAll();
    
    $corrected = 0;
    $corrections = [];
    
    foreach ($structures as $structure) {
        $lat = (float) $structure['latitude'];
        $lng = (float) $structure['longitude'];
        $needsCorrection = false;
        $correctionReason = '';
        $newLat = $lat;
        $newLng = $lng;
        
        // Vérifier si les coordonnées sont complètement invalides
        if (abs($lat) > 90 || abs($lng) > 180) {
            $needsCorrection = true;
            $correctionReason = 'Coordonnées invalides (hors limites mondiales)';
            $newLat = DEFAULT_LAT;
            $newLng = DEFAULT_LNG;
        }
        // Vérifier si les coordonnées sont en dehors du Burundi
        else if ($lat < BURUNDI_LAT_MIN || $lat > BURUNDI_LAT_MAX || 
                 $lng < BURUNDI_LNG_MIN || $lng > BURUNDI_LNG_MAX) {
            $needsCorrection = true;
            $correctionReason = 'Coordonnées en dehors du Burundi';
            
            // Essayer de trouver une ville correspondante dans l'adresse
            $cityFound = false;
            foreach ($burundiCities as $cityName => $coords) {
                if (stripos($structure['adresse'], $cityName) !== false) {
                    $newLat = $coords['lat'];
                    $newLng = $coords['lng'];
                    $correctionReason .= " - Corrigé vers $cityName";
                    $cityFound = true;
                    break;
                }
            }
            
            if (!$cityFound) {
                $newLat = DEFAULT_LAT;
                $newLng = DEFAULT_LNG;
                $correctionReason .= ' - Corrigé vers Bujumbura (défaut)';
            }
        }
        
        // Appliquer la correction si nécessaire
        if ($needsCorrection) {
            $updateSql = "UPDATE structure SET latitude = ?, longitude = ? WHERE id = ?";
            $updateStmt = $pdo->prepare($updateSql);
            $updateStmt->execute([$newLat, $newLng, $structure['id']]);
            
            $corrections[] = [
                'id' => $structure['id'],
                'nom' => $structure['nom'],
                'adresse' => $structure['adresse'],
                'old_coordinates' => ['lat' => $lat, 'lng' => $lng],
                'new_coordinates' => ['lat' => $newLat, 'lng' => $newLng],
                'reason' => $correctionReason
            ];
            
            $corrected++;
        }
    }
    
    // Retourner le résultat
    echo json_encode([
        'success' => true,
        'message' => "Correction terminée: $corrected structures corrigées sur " . count($structures) . " analysées",
        'corrected_count' => $corrected,
        'total_count' => count($structures),
        'corrections' => $corrections
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Erreur lors de la correction: ' . $e->getMessage()
    ]);
}

/**
 * Fonction pour valider les coordonnées du Burundi
 */
function isValidBurundiCoordinate($lat, $lng) {
    return $lat >= BURUNDI_LAT_MIN && 
           $lat <= BURUNDI_LAT_MAX && 
           $lng >= BURUNDI_LNG_MIN && 
           $lng <= BURUNDI_LNG_MAX;
}

/**
 * Fonction pour calculer la distance entre deux points
 */
function calculateDistance($lat1, $lng1, $lat2, $lng2) {
    $R = 6371; // Rayon de la Terre en km
    $dLat = deg2rad($lat2 - $lat1);
    $dLng = deg2rad($lng2 - $lng1);
    $a = sin($dLat/2) * sin($dLat/2) +
         cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
         sin($dLng/2) * sin($dLng/2);
    $c = 2 * atan2(sqrt($a), sqrt(1-$a));
    return $R * $c;
}
?>
