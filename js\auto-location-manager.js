/**
 * Gestionnaire de Localisation Automatique - Umwana Voice
 * Système intelligent pour optimiser l'expérience utilisateur
 */

class AutoLocationManager {
    constructor() {
        this.smartGeo = null;
        this.isInitialized = false;
        this.userLocation = null;
        this.centres = [];
        this.autoDetectionAttempted = false;
        
        // Configuration optimisée - AUTOMATISATION COMPLÈTE
        this.settings = {
            enableAutoDetection: true,           // ✅ Auto-détection au chargement
            enableContinuousTracking: true,      // ✅ Surveillance continue activée
            enableSmartFallbacks: true,          // ✅ Fallbacks intelligents
            enableLocationMemory: true,          // ✅ Mémorisation position
            showDetailedProgress: true,          // ✅ Affichage progrès détaillé
            autoCalculateDistances: true,        // ✅ Calcul automatique distances
            autoUpdateInterface: true,           // ✅ Mise à jour interface automatique
            enableLoginDetection: true,          // ✅ NOUVEAU: Auto-détection à la connexion
            enablePageLoadDetection: true,       // ✅ NOUVEAU: Auto-détection au chargement page
            enableIntelligentRetry: true,        // ✅ NOUVEAU: Retry intelligent
            enableBatteryOptimization: true,     // ✅ NOUVEAU: Optimisation batterie
            enablePermissionPersistence: true    // ✅ NOUVEAU: Persistance permissions
        };
        
        // Callbacks pour l'interface
        this.callbacks = {
            onLocationFound: null,
            onLocationError: null,
            onCentresLoaded: null,
            onDistancesCalculated: null,
            onInterfaceUpdate: null
        };
    }

    /**
     * Initialisation complète du système - AUTOMATISATION TOTALE
     */
    async init(callbacks = {}) {
        console.log('🚀 Initialisation AutoLocationManager - Mode Automatique Complet...');

        this.callbacks = { ...this.callbacks, ...callbacks };

        // Initialiser la géolocalisation intelligente
        await this.initSmartGeolocation();

        // Charger les centres
        await this.loadCentres();

        // NOUVEAU: Vérifier et restaurer les permissions précédentes
        await this.checkPreviousPermissions();

        // NOUVEAU: Démarrer immédiatement si permissions accordées
        if (this.settings.enablePageLoadDetection) {
            await this.startImmediateDetection();
        }

        // Démarrer la détection automatique si possible
        await this.startAutoDetection();

        // NOUVEAU: Configurer la surveillance continue intelligente
        if (this.settings.enableContinuousTracking) {
            this.setupIntelligentTracking();

            // Démarrer la surveillance continue après initialisation
            setTimeout(() => {
                if (window.continuousTracking && this.userLocation) {
                    console.log('🔄 Démarrage surveillance continue automatique...');
                    window.continuousTracking.start();
                }
            }, 3000); // Attendre 3 secondes après l'initialisation
        }

        this.isInitialized = true;
        console.log('✅ AutoLocationManager initialisé - Mode Automatique Complet Actif');
    }

    /**
     * Initialiser la géolocalisation intelligente
     */
    async initSmartGeolocation() {
        this.smartGeo = new SmartGeolocation();
        
        // Configurer les callbacks
        this.smartGeo.setCallbacks({
            onSuccess: (position) => this.handleLocationSuccess(position),
            onError: (error) => this.handleLocationError(error),
            onProgress: (progress) => this.handleLocationProgress(progress),
            onPermissionChange: (state) => this.handlePermissionChange(state)
        });
        
        // Charger la dernière position connue
        this.loadLastKnownPosition();
    }

    /**
     * Charger les centres depuis l'API
     */
    async loadCentres() {
        try {
            console.log('📥 Chargement des centres...');
            
            const response = await fetch('api/get_structures.php');
            const data = await response.json();
            
            if (data.success) {
                this.centres = data.structures;
                console.log(`✅ ${data.count} centres chargés`);
                
                if (this.callbacks.onCentresLoaded) {
                    this.callbacks.onCentresLoaded(this.centres);
                }
                
                // Si on a déjà une position, calculer les distances
                if (this.userLocation) {
                    this.calculateDistances();
                }
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('❌ Erreur chargement centres:', error);
            // Charger des données de fallback
            this.loadFallbackCentres();
        }
    }

    /**
     * Démarrer la détection automatique
     */
    async startAutoDetection() {
        if (!this.settings.enableAutoDetection || this.autoDetectionAttempted) {
            return;
        }
        
        this.autoDetectionAttempted = true;
        console.log('🎯 Démarrage détection automatique...');
        
        try {
            // Vérifier les permissions d'abord
            const permission = await this.checkGeolocationPermission();
            
            if (permission === 'granted') {
                // Permission déjà accordée - démarrer immédiatement
                console.log('✅ Permission accordée - détection immédiate');
                await this.smartGeo.getPreciseLocation({ showProgress: true });
            } else if (permission === 'prompt') {
                // Permission pas encore demandée - attendre interaction utilisateur
                console.log('⏳ Permission en attente - préparation détection intelligente');
                this.setupIntelligentDetection();
            } else {
                // Permission refusée - proposer alternatives
                console.log('❌ Permission refusée - activation fallbacks');
                this.activateFallbackMethods();
            }
        } catch (error) {
            console.warn('⚠️ Détection automatique échouée:', error);
            this.activateFallbackMethods();
        }
    }

    /**
     * Vérifier les permissions de géolocalisation
     */
    async checkGeolocationPermission() {
        try {
            if ('permissions' in navigator) {
                const permission = await navigator.permissions.query({ name: 'geolocation' });
                return permission.state;
            }
        } catch (error) {
            console.warn('Impossible de vérifier les permissions:', error);
        }
        return 'prompt';
    }

    /**
     * Configurer la détection intelligente
     */
    setupIntelligentDetection() {
        console.log('🧠 Configuration détection intelligente...');
        
        // Détecter la première interaction utilisateur
        const detectOnInteraction = () => {
            console.log('👆 Interaction détectée - démarrage géolocalisation');
            this.requestLocationWithUserConsent();
        };
        
        // Événements d'interaction
        const interactionEvents = ['click', 'touchstart', 'keydown', 'scroll'];
        
        interactionEvents.forEach(event => {
            document.addEventListener(event, detectOnInteraction, { 
                once: true, 
                passive: true 
            });
        });
        
        // Timeout pour proposer la localisation après 3 secondes
        setTimeout(() => {
            if (!this.userLocation) {
                this.suggestLocationDetection();
            }
        }, 3000);
    }

    /**
     * Demander la localisation avec consentement utilisateur
     */
    async requestLocationWithUserConsent() {
        try {
            console.log('🤝 Demande de localisation avec consentement...');
            
            if (this.callbacks.onLocationProgress) {
                this.callbacks.onLocationProgress({
                    type: 'consent_request',
                    message: 'Demande d\'autorisation de localisation...'
                });
            }
            
            await this.smartGeo.getPreciseLocation({ 
                showProgress: true,
                autoFallback: true 
            });
        } catch (error) {
            console.warn('Demande de localisation échouée:', error);
            this.activateFallbackMethods();
        }
    }

    /**
     * Suggérer la détection de localisation
     */
    suggestLocationDetection() {
        console.log('💡 Suggestion de localisation...');
        
        // Afficher une notification discrète
        this.showLocationSuggestion();
    }

    /**
     * Afficher une suggestion de localisation
     */
    showLocationSuggestion() {
        // Créer une notification discrète
        const suggestion = document.createElement('div');
        suggestion.className = 'location-suggestion';
        suggestion.innerHTML = `
            <div class="bg-blue-500 text-white p-3 rounded-lg shadow-lg fixed top-4 right-4 z-50 max-w-sm">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="text-xl mr-2">📍</span>
                        <span class="text-sm">Localisation pour centres proches ?</span>
                    </div>
                    <div class="flex gap-2 ml-3">
                        <button onclick="autoLocationManager.acceptLocationSuggestion()" 
                                class="bg-white text-blue-500 px-2 py-1 rounded text-xs font-medium">
                            Oui
                        </button>
                        <button onclick="autoLocationManager.dismissLocationSuggestion()" 
                                class="bg-blue-600 text-white px-2 py-1 rounded text-xs">
                            Plus tard
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(suggestion);
        
        // Auto-suppression après 10 secondes
        setTimeout(() => {
            if (suggestion.parentNode) {
                suggestion.remove();
            }
        }, 10000);
    }

    /**
     * Accepter la suggestion de localisation
     */
    async acceptLocationSuggestion() {
        document.querySelector('.location-suggestion')?.remove();
        await this.requestLocationWithUserConsent();
    }

    /**
     * Rejeter la suggestion de localisation
     */
    dismissLocationSuggestion() {
        document.querySelector('.location-suggestion')?.remove();
        console.log('🚫 Suggestion de localisation rejetée');
        
        // Sauvegarder la préférence
        localStorage.setItem('umwana_location_suggestion_dismissed', Date.now().toString());
    }

    /**
     * Activer les méthodes de fallback
     */
    activateFallbackMethods() {
        console.log('🔄 Activation des méthodes de fallback...');
        
        // Essayer de charger la dernière position connue
        const lastPosition = this.loadLastKnownPosition();
        
        if (lastPosition) {
            console.log('📍 Utilisation de la dernière position connue');
            this.handleLocationSuccess(lastPosition, { fromCache: true });
        } else {
            // Proposer la sélection manuelle
            this.suggestManualSelection();
        }
    }

    /**
     * Suggérer la sélection manuelle
     */
    suggestManualSelection() {
        console.log('🎯 Suggestion de sélection manuelle...');
        
        if (this.callbacks.onLocationError) {
            this.callbacks.onLocationError({
                code: 'SUGGEST_MANUAL',
                message: 'Sélectionnez votre position sur la carte pour voir les centres proches',
                canFallback: true,
                suggestManual: true
            });
        }
    }

    /**
     * Gérer le succès de localisation
     */
    handleLocationSuccess(position, options = {}) {
        console.log('✅ Position obtenue:', position);
        
        this.userLocation = position;
        
        // Sauvegarder la position
        if (this.settings.enableLocationMemory && !options.fromCache) {
            this.saveLastKnownPosition(position);
        }
        
        // Calculer les distances si les centres sont chargés
        if (this.centres.length > 0) {
            this.calculateDistances();
        }
        
        // Notifier l'interface
        if (this.callbacks.onLocationFound) {
            this.callbacks.onLocationFound(position, options);
        }
        
        // Démarrer la surveillance continue si activée
        if (this.settings.enableContinuousTracking) {
            this.smartGeo.updateConfig({ enableContinuousTracking: true });

            // NOUVEAU: Démarrer le système de surveillance continue avancé
            setTimeout(() => {
                if (window.continuousTracking && !window.continuousTracking.isActive) {
                    console.log('🚀 Activation surveillance continue avancée...');
                    window.continuousTracking.start();
                }
            }, 2000);
        }
    }

    /**
     * Gérer les erreurs de localisation
     */
    handleLocationError(error) {
        console.warn('⚠️ Erreur de localisation:', error);
        
        if (this.callbacks.onLocationError) {
            this.callbacks.onLocationError(error);
        }
        
        // Activer les fallbacks si approprié
        if (this.settings.enableSmartFallbacks) {
            this.activateFallbackMethods();
        }
    }

    /**
     * Gérer le progrès de localisation
     */
    handleLocationProgress(progress) {
        console.log('📊 Progrès localisation:', progress);
        
        if (this.callbacks.onLocationProgress) {
            this.callbacks.onLocationProgress(progress);
        }
    }

    /**
     * Gérer les changements de permission
     */
    handlePermissionChange(state) {
        console.log('🔄 Changement de permission:', state);
        
        if (state === 'granted' && !this.userLocation) {
            // Permission accordée - relancer la détection
            this.startAutoDetection();
        }
    }

    /**
     * Calculer les distances
     */
    calculateDistances() {
        if (!this.userLocation || this.centres.length === 0) return;
        
        console.log('📏 Calcul des distances...');
        
        this.centres.forEach(centre => {
            centre.distance = this.calculateDistance(
                this.userLocation.latitude,
                this.userLocation.longitude,
                parseFloat(centre.latitude),
                parseFloat(centre.longitude)
            );
        });
        
        // Trier par distance et urgence
        this.centres.sort((a, b) => {
            if (a.urgence && !b.urgence) return -1;
            if (!a.urgence && b.urgence) return 1;
            return a.distance - b.distance;
        });
        
        if (this.callbacks.onDistancesCalculated) {
            this.callbacks.onDistancesCalculated(this.centres);
        }
        
        console.log(`✅ Distances calculées pour ${this.centres.length} centres`);
    }

    /**
     * Calculer la distance entre deux points
     */
    calculateDistance(lat1, lng1, lat2, lng2) {
        const R = 6371; // Rayon de la Terre en km
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLng = (lng2 - lng1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                  Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }

    /**
     * Sauvegarder la dernière position connue
     */
    saveLastKnownPosition(position) {
        try {
            const positionData = {
                ...position,
                savedAt: Date.now()
            };
            localStorage.setItem('umwana_last_position', JSON.stringify(positionData));
        } catch (error) {
            console.warn('Erreur sauvegarde position:', error);
        }
    }

    /**
     * Charger la dernière position connue
     */
    loadLastKnownPosition() {
        try {
            const saved = localStorage.getItem('umwana_last_position');
            if (saved) {
                const positionData = JSON.parse(saved);
                
                // Vérifier que la position n'est pas trop ancienne (24h)
                const maxAge = 24 * 60 * 60 * 1000; // 24 heures
                if (Date.now() - positionData.savedAt < maxAge) {
                    console.log('📍 Position sauvegardée trouvée');
                    return positionData;
                }
            }
        } catch (error) {
            console.warn('Erreur chargement position sauvegardée:', error);
        }
        return null;
    }

    /**
     * Charger des centres de fallback
     */
    loadFallbackCentres() {
        console.log('🔄 Chargement centres de fallback...');
        
        // Centres d'exemple pour Bujumbura
        this.centres = [
            {
                id: 1,
                nom: "Centre d'Urgence Bujumbura",
                latitude: -3.3614,
                longitude: 29.3599,
                type_nom: "CENTRE D'URGENCE",
                urgence: true,
                adresse: "Centre-ville, Bujumbura",
                telephone: "+257 22 123 456"
            },
            {
                id: 2,
                nom: "Orphelinat Espoir",
                latitude: -3.3500,
                longitude: 29.3700,
                type_nom: "ORPHELINAT",
                urgence: false,
                adresse: "Kamenge, Bujumbura",
                telephone: "+257 22 789 012"
            }
        ];
        
        if (this.callbacks.onCentresLoaded) {
            this.callbacks.onCentresLoaded(this.centres);
        }
    }

    /**
     * Mettre à jour les paramètres
     */
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        
        // Appliquer les nouveaux paramètres
        if (this.smartGeo) {
            this.smartGeo.updateConfig({
                enableContinuousTracking: this.settings.enableContinuousTracking
            });
        }
    }

    /**
     * Obtenir la position actuelle
     */
    getCurrentPosition() {
        return this.userLocation;
    }

    /**
     * Obtenir les centres
     */
    getCentres() {
        return this.centres;
    }

    /**
     * Forcer une nouvelle détection
     */
    async forceLocationDetection() {
        console.log('🔄 Détection forcée...');
        
        if (this.smartGeo) {
            await this.smartGeo.getPreciseLocation({ 
                showProgress: true,
                autoFallback: false 
            });
        }
    }

    /**
     * NOUVEAU: Vérifier les permissions précédentes
     */
    async checkPreviousPermissions() {
        if (!this.settings.enablePermissionPersistence) return;

        try {
            const savedPermission = localStorage.getItem('umwana_geolocation_permission');
            if (savedPermission === 'granted') {
                console.log('🔓 Permissions précédentes trouvées - activation immédiate');
                return true;
            }
        } catch (error) {
            console.warn('Erreur vérification permissions:', error);
        }
        return false;
    }

    /**
     * NOUVEAU: Démarrage immédiat si permissions accordées
     */
    async startImmediateDetection() {
        try {
            const permission = await this.checkGeolocationPermission();
            if (permission === 'granted') {
                console.log('🚀 Démarrage immédiat - permissions accordées');
                await this.smartGeo.getPreciseLocation({
                    showProgress: false, // Mode silencieux pour démarrage automatique
                    autoFallback: true
                });
            }
        } catch (error) {
            console.log('Démarrage immédiat échoué, passage en mode standard');
        }
    }

    /**
     * NOUVEAU: Configuration surveillance continue intelligente
     */
    setupIntelligentTracking() {
        console.log('🧠 Configuration surveillance continue intelligente...');

        // Optimisation batterie - réduire fréquence si batterie faible
        if (this.settings.enableBatteryOptimization && 'getBattery' in navigator) {
            navigator.getBattery().then(battery => {
                const updateTrackingFrequency = () => {
                    if (battery.level < 0.2) { // Batterie < 20%
                        console.log('🔋 Batterie faible - réduction fréquence tracking');
                        this.smartGeo.updateConfig({
                            trackingInterval: 300000, // 5 minutes
                            enableHighAccuracy: false
                        });
                    } else if (battery.level < 0.5) { // Batterie < 50%
                        this.smartGeo.updateConfig({
                            trackingInterval: 120000, // 2 minutes
                            enableHighAccuracy: true
                        });
                    } else { // Batterie > 50%
                        this.smartGeo.updateConfig({
                            trackingInterval: 60000, // 1 minute
                            enableHighAccuracy: true
                        });
                    }
                };

                updateTrackingFrequency();
                battery.addEventListener('levelchange', updateTrackingFrequency);
            }).catch(() => {
                console.log('API Batterie non supportée - tracking standard');
            });
        }
    }

    /**
     * NOUVEAU: Déclenchement automatique à la connexion
     */
    async triggerLoginDetection() {
        if (!this.settings.enableLoginDetection) return;

        console.log('🔐 Déclenchement géolocalisation post-connexion...');

        // Attendre un court délai pour que la page se charge
        setTimeout(async () => {
            try {
                await this.smartGeo.getPreciseLocation({
                    showProgress: true,
                    autoFallback: true
                });

                // Sauvegarder la permission accordée
                if (this.settings.enablePermissionPersistence) {
                    localStorage.setItem('umwana_geolocation_permission', 'granted');
                }
            } catch (error) {
                console.log('Géolocalisation post-connexion échouée:', error);
            }
        }, 1500);
    }

    /**
     * NOUVEAU: Retry intelligent avec backoff exponentiel
     */
    async intelligentRetry(attemptNumber = 1) {
        if (!this.settings.enableIntelligentRetry || attemptNumber > 3) return;

        const delay = Math.pow(2, attemptNumber) * 1000; // 2s, 4s, 8s
        console.log(`🔄 Retry intelligent #${attemptNumber} dans ${delay/1000}s...`);

        setTimeout(async () => {
            try {
                await this.smartGeo.getPreciseLocation({
                    showProgress: attemptNumber === 1,
                    autoFallback: true
                });
            } catch (error) {
                this.intelligentRetry(attemptNumber + 1);
            }
        }, delay);
    }

    /**
     * Nettoyer les ressources
     */
    destroy() {
        if (this.smartGeo) {
            this.smartGeo.destroy();
        }

        // Supprimer les suggestions actives
        document.querySelector('.location-suggestion')?.remove();

        this.isInitialized = false;
        console.log('🧹 AutoLocationManager nettoyé');
    }
}

// Instance globale
window.autoLocationManager = new AutoLocationManager();
