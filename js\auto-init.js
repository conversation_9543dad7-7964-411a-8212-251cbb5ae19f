/**
 * Script d'Initialisation Automatique - Umwana Voice
 * Démarrage automatique de la géolocalisation sur toutes les pages
 */

class AutoInit {
    constructor() {
        this.isInitialized = false;
        this.autoLocationManager = null;
        this.currentPage = this.detectCurrentPage();
        
        console.log(`🚀 AutoInit démarré pour page: ${this.currentPage}`);
    }

    /**
     * Détecter la page actuelle
     */
    detectCurrentPage() {
        const path = window.location.pathname;
        const filename = path.split('/').pop();
        
        if (filename.includes('dashboard')) return 'dashboard';
        if (filename.includes('login')) return 'login';
        if (filename.includes('Home1')) return 'home';
        if (filename.includes('gestion')) return 'gestion';
        if (filename.includes('centre')) return 'centre';
        
        return 'other';
    }

    /**
     * Initialisation automatique selon la page
     */
    async init() {
        if (this.isInitialized) return;
        
        console.log(`🎯 Initialisation automatique pour: ${this.currentPage}`);
        
        try {
            // Attendre que les scripts soient chargés
            await this.waitForScripts();
            
            // Initialiser selon le type de page
            switch (this.currentPage) {
                case 'dashboard':
                    await this.initDashboard();
                    break;
                case 'login':
                    await this.initLogin();
                    break;
                case 'home':
                    await this.initHome();
                    break;
                case 'gestion':
                case 'centre':
                    await this.initGestionPages();
                    break;
                default:
                    await this.initGeneric();
                    break;
            }
            
            this.isInitialized = true;
            console.log('✅ AutoInit terminé avec succès');
            
        } catch (error) {
            console.error('❌ Erreur AutoInit:', error);
        }
    }

    /**
     * Attendre que les scripts nécessaires soient chargés
     */
    async waitForScripts() {
        return new Promise((resolve) => {
            const checkScripts = () => {
                if (window.AutoLocationManager && window.SmartGeolocation) {
                    resolve();
                } else {
                    setTimeout(checkScripts, 100);
                }
            };
            checkScripts();
        });
    }

    /**
     * Initialisation pour les pages dashboard
     */
    async initDashboard() {
        console.log('📊 Initialisation Dashboard avec géolocalisation automatique...');
        
        this.autoLocationManager = window.autoLocationManager;
        
        await this.autoLocationManager.init({
            onLocationFound: (position) => {
                console.log('📍 Position Dashboard:', position);
                
                // Créer un indicateur de position dans le dashboard
                this.createLocationIndicator(position);
                
                // Notifier l'utilisateur
                this.showLocationNotification(position);
            },
            onLocationError: (error) => {
                console.warn('⚠️ Erreur géolocalisation Dashboard:', error);
                
                // Afficher une notification discrète
                if (!error.suggestManual) {
                    this.showLocationError(error);
                }
            }
        });
    }

    /**
     * Initialisation pour les pages de connexion
     */
    async initLogin() {
        console.log('🔐 Préparation géolocalisation post-connexion...');
        
        // Préparer la géolocalisation pour après la connexion
        this.autoLocationManager = window.autoLocationManager;
        
        // Écouter les événements de connexion réussie
        this.setupLoginListener();
    }

    /**
     * Initialisation pour la page d'accueil
     */
    async initHome() {
        console.log('🏠 Initialisation Home avec géolocalisation complète...');
        
        // La page Home1.php a déjà sa propre initialisation
        // On s'assure juste que tout est prêt
        this.autoLocationManager = window.autoLocationManager;
    }

    /**
     * Initialisation pour les pages de gestion
     */
    async initGestionPages() {
        console.log('⚙️ Initialisation pages gestion avec géolocalisation...');
        
        this.autoLocationManager = window.autoLocationManager;
        
        await this.autoLocationManager.init({
            onLocationFound: (position) => {
                console.log('📍 Position Gestion:', position);
                
                // Sauvegarder la position pour utilisation dans les formulaires
                this.savePositionForForms(position);
                
                // Afficher un indicateur discret
                this.createLocationIndicator(position, true);
            },
            onLocationError: (error) => {
                console.warn('⚠️ Erreur géolocalisation Gestion:', error);
            }
        });
    }

    /**
     * Initialisation générique
     */
    async initGeneric() {
        console.log('🌐 Initialisation générique avec géolocalisation...');
        
        this.autoLocationManager = window.autoLocationManager;
        
        await this.autoLocationManager.init({
            onLocationFound: (position) => {
                console.log('📍 Position générique:', position);
                this.savePositionForForms(position);
            },
            onLocationError: (error) => {
                console.log('⚠️ Géolocalisation non disponible sur cette page');
            }
        });
    }

    /**
     * Créer un indicateur de position
     */
    createLocationIndicator(position, discrete = false) {
        // Supprimer l'indicateur existant
        const existing = document.getElementById('location-indicator');
        if (existing) existing.remove();
        
        const indicator = document.createElement('div');
        indicator.id = 'location-indicator';
        indicator.className = discrete ? 'location-indicator-discrete' : 'location-indicator';
        
        const accuracy = position.accuracy < 10 ? 'excellente' : 
                        position.accuracy < 50 ? 'bonne' : 'acceptable';
        
        indicator.innerHTML = `
            <div class="flex items-center space-x-2 bg-green-100 text-green-800 px-3 py-2 rounded-lg text-sm">
                <span class="text-lg">📍</span>
                <span>Position détectée (précision ${accuracy})</span>
                <button onclick="this.parentElement.parentElement.remove()" class="text-green-600 hover:text-green-800">×</button>
            </div>
        `;
        
        // Ajouter au début du body ou dans un conteneur spécifique
        const container = document.querySelector('.container') || document.body;
        container.insertBefore(indicator, container.firstChild);
        
        // Auto-suppression après 10 secondes si discret
        if (discrete) {
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.remove();
                }
            }, 10000);
        }
    }

    /**
     * Afficher une notification de position
     */
    showLocationNotification(position) {
        // Utiliser showCustomAlert si disponible, sinon notification simple
        if (typeof showCustomAlert === 'function') {
            const accuracy = position.accuracy < 10 ? 'excellente' : 
                            position.accuracy < 50 ? 'bonne' : 'acceptable';
            showCustomAlert('Position automatique', 
                `Votre position a été détectée automatiquement avec une précision ${accuracy}`, 
                'success');
        } else {
            console.log('✅ Position détectée automatiquement');
        }
    }

    /**
     * Afficher une erreur de géolocalisation
     */
    showLocationError(error) {
        if (typeof showCustomAlert === 'function') {
            showCustomAlert('Géolocalisation', 
                'La géolocalisation automatique n\'est pas disponible sur cette page', 
                'info');
        }
    }

    /**
     * Sauvegarder la position pour les formulaires
     */
    savePositionForForms(position) {
        // Sauvegarder dans sessionStorage pour utilisation dans les formulaires
        sessionStorage.setItem('current_user_position', JSON.stringify({
            latitude: position.latitude,
            longitude: position.longitude,
            accuracy: position.accuracy,
            timestamp: Date.now()
        }));
        
        console.log('💾 Position sauvegardée pour les formulaires');
    }

    /**
     * Configurer l'écoute des événements de connexion
     */
    setupLoginListener() {
        // Écouter les redirections après connexion
        const originalPushState = history.pushState;
        const originalReplaceState = history.replaceState;
        
        const handleNavigation = () => {
            if (window.location.pathname.includes('dashboard') || 
                window.location.pathname.includes('gestion')) {
                
                console.log('🔐 Connexion détectée - déclenchement géolocalisation...');
                
                setTimeout(async () => {
                    if (this.autoLocationManager) {
                        await this.autoLocationManager.triggerLoginDetection();
                    }
                }, 1000);
            }
        };
        
        history.pushState = function() {
            originalPushState.apply(history, arguments);
            handleNavigation();
        };
        
        history.replaceState = function() {
            originalReplaceState.apply(history, arguments);
            handleNavigation();
        };
        
        window.addEventListener('popstate', handleNavigation);
    }
}

// Styles CSS pour les indicateurs
const styles = `
<style>
.location-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    animation: slideInRight 0.3s ease-out;
}

.location-indicator-discrete {
    position: relative;
    margin: 10px 0;
    animation: fadeIn 0.3s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
</style>
`;

// Injecter les styles
document.head.insertAdjacentHTML('beforeend', styles);

// Initialisation automatique
const autoInit = new AutoInit();

// Démarrer dès que le DOM est prêt
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => autoInit.init());
} else {
    autoInit.init();
}

// Export global
window.autoInit = autoInit;
