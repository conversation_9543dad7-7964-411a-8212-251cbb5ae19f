<?php
session_start();

// Connexion à la base de données
try {
    $pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Erreur de connexion : " . $e->getMessage());
}

// Récupération des données du formulaire
$email = $_POST['email'] ?? '';
$mot_de_passe = $_POST['mot_de_passe'] ?? '';

// Vérification de l'utilisateur
$sql = "SELECT * FROM parrain WHERE email = :email";
$stmt = $pdo->prepare($sql);
$stmt->execute(['email' =>   $email]);
$parrain = $stmt->fetch(PDO::FETCH_ASSOC);

// Vérifier si le mot de passe correspond (avec hash)
if ($parrain && password_verify($mot_de_passe, $parrain['mot_de_passe'])) {
    // Connexion réussie
    $_SESSION['parrain_id'] = $parrain['id'];
    $_SESSION['nom'] = $parrain['nom'];
    header("Location: dashboard_parrain.php");
    exit();
} else {
    // Connexion échouée
    echo "<script>alert('Email ou mot de passe incorrect'); window.location.href='connexion_parrain.php';</script>";
}
?>
