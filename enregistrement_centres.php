<?php
// Connexion à la base de données avec PDO
try {
    $pdo = new PDO("mysql:host=localhost;dbname=gestion_enfant", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $nom = $_POST['nom'];
        $adresse = $_POST['adresse'];
        $latitude = $_POST['latitude'];
        $longitude = $_POST['longitude'];
        $capacite_max = $_POST['capacite_max'];
        $capacite_actuelle = $_POST['capacite_actuelle'];
        $telephone = $_POST['telephone'];
        $email = $_POST['email'];
        $responsable = $_POST['responsable'];
        $type_structure_id = 4; // ID pour les centres d'accueil

        // Insertion dans la base de données
        $stmt = $pdo->prepare("INSERT INTO structure (nom, adresse, latitude, longitude, capacite_max, capacite_actuelle, telephone, email, responsable, type_structure_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([$nom, $adresse, $latitude, $longitude, $capacite_max, $capacite_actuelle, $telephone, $email, $responsable, $type_structure_id]);

        echo "<script>alert('Centre enregistré avec succès !');</script>";
    }
} catch (PDOException $e) {
    echo "Erreur de connexion : " . $e->getMessage();
}
?>

<?php include 'header_centre.php'; ?>

<div class="container">
    <h2>Enregistrement d'un Centre d'Accueil</h2>
    <form method="POST" action="">
        <div class="form-group">
            <label for="nom">Nom du Centre:</label>
            <input type="text" id="nom" name="nom" required>
        </div>
        <div class="form-group">
            <label for="adresse">Adresse:</label>
            <input type="text" id="adresse" name="adresse" required>
        </div>
        <div class="form-group">
            <label for="latitude">Latitude:</label>
            <input type="text" id="latitude" name="latitude" required>
        </div>
        <div class="form-group">
            <label for="longitude">Longitude:</label>
            <input type="text" id="longitude" name="longitude" required>
        </div>
        <div class="form-group">
            <label for="capacite_max">Capacité Max:</label>
            <input type="number" id="capacite_max" name="capacite_max" required>
        </div>
        <div class="form-group">
            <label for="capacite_actuelle">Capacité Actuelle:</label>
            <input type="number" id="capacite_actuelle" name="capacite_actuelle" required>
        </div>
        <div class="form-group">
            <label for="telephone">Téléphone:</label>
            <input type="text" id="telephone" name="telephone" required>
        </div>
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" required>
        </div>
        <div class="form-group">
            <label for="responsable">Responsable:</label>
            <input type="text" id="responsable" name="responsable" required>
        </div>
        <button type="submit" class="btn-submit">Enregistrer</button>
    </form>
</div>