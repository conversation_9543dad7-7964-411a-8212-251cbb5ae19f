<?php
session_start();
$pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');

if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $structure_id = $_POST['structure_id'];
    $genre = $_POST['genre'];
    $tranche = $_POST['tranche_age'];
    $handicap = $_POST['handicap'];

    $age_min = 1; $age_max = 5;
    if ($tranche === "6-12") { $age_min = 6; $age_max = 12; }
    if ($tranche === "12-18") { $age_min = 12; $age_max = 18; }

    $sexe_id = ($genre === "masculin") ? 1 : 2;

    $stmt = $pdo->prepare("
        SELECT id, matricule, nom, prenom, date_naissance, photo_portrait 
        FROM enfant 
        WHERE structure_id = ? AND sexe_id = ? AND handicap = ? 
        AND TIMESTAMPDIFF(YEAR, date_naissance, CURDATE()) BETWEEN ? AND ?
    ");
    $stmt->execute([$structure_id, $sexe_id, $handicap, $age_min, $age_max]);
    $enfants = $stmt->fetchAll(PDO::FETCH_ASSOC);
} else {
    header("Location: formulaire_parrainage.php");
    exit;
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Résultats des enfants</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
<div class="container mt-5">
  <h2>Enfants correspondant à votre recherche</h2>
  <?php if (empty($enfants)): ?>
    <div class="alert alert-warning">Aucun enfant trouvé selon vos critères.</div>
  <?php else: ?>
    <div class="row">
      <?php foreach ($enfants as $e): ?>
        <div class="col-md-4 mb-4">
          <div class="card shadow">
            <?php if ($e['photo_portrait']): ?>
              <img src="<?= $e['photo_portrait'] ?>" class="card-img-top" style="height:200px;object-fit:cover;">
            <?php endif; ?>
            <div class="card-body">
              <h5><?= htmlspecialchars($e['prenom'] . ' ' . $e['nom']) ?></h5>
              <p>Matricule : <?= $e['matricule'] ?></p>
              <a href="details_enfant.php?id=<?= $e['id'] ?>" class="btn btn-primary btn-sm">Afficher</a>
            </div>
          </div>
        </div>
      <?php endforeach; ?>
    </div>
  <?php endif; ?>
</div>
</body>
</html>
