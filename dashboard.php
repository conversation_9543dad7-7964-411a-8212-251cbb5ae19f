<?php
// Connexion à la base de données pour récupérer les statistiques réelles
require_once 'config.php';

try {
    // Statistiques réelles de la base de données
    $stats_utilisateurs = $pdo->query("SELECT COUNT(*) FROM utilisateur WHERE actif = 1")->fetchColumn();
    $stats_centres = $pdo->query("SELECT COUNT(*) FROM structure WHERE active = 1")->fetchColumn();
    $stats_enfants = $pdo->query("SELECT COUNT(*) FROM enfant")->fetchColumn();
    $stats_projets = $pdo->query("SELECT COUNT(*) FROM projet WHERE statut_projet_id = 2")->fetchColumn();
    $stats_parrainages = $pdo->query("SELECT COUNT(*) FROM demande_parrainage WHERE statut = 'valide'")->fetchColumn();
    $stats_demandes_attente = $pdo->query("SELECT COUNT(*) FROM demande_parrainage WHERE statut = 'en_attente'")->fetchColumn();

    // Capacités
    $capacite_totale = $pdo->query("SELECT SUM(capacite_max) FROM structure WHERE active = 1")->fetchColumn() ?: 0;
    $capacite_utilisee = $pdo->query("SELECT SUM(capacite_actuelle) FROM structure WHERE active = 1")->fetchColumn() ?: 0;

} catch (PDOException $e) {
    // Valeurs par défaut en cas d'erreur
    $stats_utilisateurs = 0;
    $stats_centres = 0;
    $stats_enfants = 0;
    $stats_projets = 0;
    $stats_parrainages = 0;
    $stats_demandes_attente = 0;
    $capacite_totale = 0;
    $capacite_utilisee = 0;
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Tableau de Bord - Ministère</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            font-family: 'Segoe UI', sans-serif;
            background-color: #f5f7fa;
        }

        /* Barre du haut */
        header {
            background-color: #34495e;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 30px;
        }

        .titre-header {
            font-size: 20px;
            font-weight: bold;
        }

        .btn-deconnexion {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
        }

        .container {
            padding: 30px;
        }

        /* Section statistiques */
        .section-stats {
            margin-bottom: 40px;
        }

        .section-stats h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            font-size: 2.5em;
            margin-right: 15px;
        }

        .stat-content h3 {
            font-size: 2em;
            margin: 0;
            color: #2c3e50;
            font-weight: bold;
        }

        .stat-content p {
            margin: 5px 0 0 0;
            color: #7f8c8d;
            font-size: 0.9em;
        }

        /* Indicateur de capacité */
        .capacity-indicator {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .capacity-indicator h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .capacity-bar {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .capacity-used {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #f39c12, #e74c3c);
            transition: width 0.5s ease;
        }

        .section-cartes {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 40px;
        }

        .carte {
            flex: 1;
            min-width: 250px;
            background-color: #ffffff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.2s, background-color 0.2s;
            cursor: pointer;
            text-decoration: none;
            color: #2c3e50;
            position: relative;
        }

        .carte:hover {
            transform: scale(1.02);
            background-color: #ecf0f1;
        }

        .carte-stat {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .carte h3 {
            margin-bottom: 10px;
        }

        .section-actions {
            background-color: #ffffff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .section-actions h2 {
            margin-bottom: 20px;
            color: #2c3e50;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
        }

        .action {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
            transition: background-color 0.2s;
            cursor: pointer;
        }

        .action:hover {
            background-color: #d0d7de;
        }

        footer {
            margin-top: 50px;
            background-color: #34495e;
            color: white;
            text-align: center;
            padding: 15px;
        }

        @media (max-width: 600px) {
            header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .carte {
                width: 100%;
            }
        }
    </style>
</head>
<body>

<header>
    <div class="titre-header">Tableau de Bord</div>
    <button class="btn-deconnexion">Déconnexion</button>
</header>

<div class="container">

    <!-- Statistiques en temps réel -->
    <div class="section-stats">
        <h2>📊 Statistiques en Temps Réel</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-content">
                    <h3><?php echo number_format($stats_utilisateurs); ?></h3>
                    <p>Utilisateurs Actifs</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🏠</div>
                <div class="stat-content">
                    <h3><?php echo number_format($stats_centres); ?></h3>
                    <p>Structures Actives</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">👶</div>
                <div class="stat-content">
                    <h3><?php echo number_format($stats_enfants); ?></h3>
                    <p>Enfants Enregistrés</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📋</div>
                <div class="stat-content">
                    <h3><?php echo number_format($stats_projets); ?></h3>
                    <p>Projets Actifs</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🤝</div>
                <div class="stat-content">
                    <h3><?php echo number_format($stats_parrainages); ?></h3>
                    <p>Parrainages Validés</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">⏳</div>
                <div class="stat-content">
                    <h3><?php echo number_format($stats_demandes_attente); ?></h3>
                    <p>Demandes en Attente</p>
                </div>
            </div>
        </div>

        <!-- Indicateur de capacité -->
        <div class="capacity-indicator">
            <h3>📊 Capacité d'Accueil</h3>
            <div class="capacity-bar">
                <div class="capacity-used" style="width: <?php echo $capacite_totale > 0 ? ($capacite_utilisee / $capacite_totale * 100) : 0; ?>%"></div>
            </div>
            <p><?php echo number_format($capacite_utilisee); ?> / <?php echo number_format($capacite_totale); ?> places occupées
               (<?php echo $capacite_totale > 0 ? round($capacite_utilisee / $capacite_totale * 100, 1) : 0; ?>%)</p>
        </div>
    </div>

    <!-- Cartes principales -->
    <div class="section-cartes">
        <a href="gestion utilisateur.php" class="carte">
            <h3>Gestion des Utilisateurs</h3>
            <p>Accéder à la liste, ajout ou modification des utilisateurs.</p>
            <div class="carte-stat"><?php echo $stats_utilisateurs; ?> actifs</div>
        </a>
        <a href="gestion centres.php" class="carte">
            <h3>Gestion des Centres</h3>
            <p>Voir, ajouter ou modifier les centres d'accueil.</p>
            <div class="carte-stat"><?php echo $stats_centres; ?> centres</div>
        </a>
        <a href="gestion partenaires.php" class="carte">
            <h3>Gestion des Partenaires</h3>
            <p>Gérer les partenaires institutionnels et ONG.</p>
            <div class="carte-stat">Partenaires actifs</div>
        </a>
    </div>

    <!-- Actions rapides -->
    <div class="section-actions">
        <h2>Actions Rapides</h2>
        <div class="actions-grid">
            <div class="action" onclick="window.location='orphelinats_gestion.php'">Liste des Orphelinats</div>
            <div class="action" onclick="window.location='gestion_enfant.php '">Gestion des Enfants</div>
            <div class="action" onclick="window.location='signalements.php'">Signalement</div>
            <div class="action" onclick="window.location='suivi_enfants.php'">Suivi des Enfants</div>
            <div class="action" onclick="window.location='parrainage.php'">Parrainage</div>
        </div>
    </div>
</div>

<footer>
    &copy; 2025 Ministère de la Solidarité Nationale - Tous droits réservés.
</footer>

<!-- Scripts de géolocalisation automatique -->
<script src="js/performance-optimizer.js"></script>
<script src="js/smart-geolocation.js"></script>
<script src="js/auto-location-manager.js"></script>
<script src="js/continuous-tracking.js"></script>
<script src="js/geolocation-preferences.js"></script>
<script src="js/geolocation-tester.js"></script>
<script src="js/auto-init.js"></script>
<script src="js/login-geolocation-trigger.js"></script>

<!-- Bouton de test pour développement -->
<script>
// Ajouter un bouton de test en mode développement
if (window.location.hostname === 'localhost' || window.location.hostname.includes('127.0.0.1')) {
    document.addEventListener('DOMContentLoaded', function() {
        const testButton = document.createElement('div');
        testButton.innerHTML = `
            <button onclick="runGeolocationTests()"
                    class="fixed top-4 left-1/2 transform -translate-x-1/2 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg shadow-lg z-40 text-sm"
                    title="Lancer les tests automatisés">
                🧪 Tests Auto
            </button>
        `;
        document.body.appendChild(testButton);
    });
}
</script>

</body>
</html>
