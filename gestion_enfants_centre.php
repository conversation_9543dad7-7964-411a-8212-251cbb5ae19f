<?php
// Connexion à la base de données avec PDO
try {
    $pdo = new PDO("mysql:host=localhost;dbname=gestion_enfant", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Récupération des enfants uniquement dans les centres d'accueil
    $stmt = $pdo->prepare("
        SELECT e.*, s.nom AS centre_nom
        FROM enfant e
        JOIN structure s ON e.structure_id = s.id
        WHERE s.type_structure_id = 4
    ");
    $stmt->execute();
    $enfants = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    echo "Erreur de connexion : " . $e->getMessage();
    $enfants = []; // Assurez-vous que $enfants est un tableau vide en cas d'erreur
}
?>

<?php include 'header_centre.php'; ?>

<div class="container">
    <h2>Liste des Enfants</h2>
    <button class="btn-submit" onclick="window.location.href='ajouter_enfant.php'">Ajouter un Enfant</button>
    <table>
        <thead>
            <tr>
                <th>Nom</th>
                <th>Prénom</th>
                <th>Centre</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($enfants)): ?>
                <?php foreach ($enfants as $enfant): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($enfant['nom']); ?></td>
                        <td><?php echo htmlspecialchars($enfant['prenom']); ?></td>
                        <td><?php echo htmlspecialchars($enfant['centre_nom']); ?></td>
                        <td>
                            <button class="btn-action" onclick="window.location.href='modifier_enfant.php?id=<?php echo $enfant['id']; ?>'">Modifier</button>
                            <button class="btn-action" onclick="if(confirm('Êtes-vous sûr de vouloir supprimer cet enfant ?')) { window.location.href='supprimer_enfant.php?id=<?php echo $enfant['id']; ?>'; }">Supprimer</button>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="4">Aucun enfant trouvé.</td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>