<?php
session_start();
$pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION); // pour afficher les erreurs PDO

// Vérifie que le parrain est connecté
if (!isset($_SESSION['parrain_id'])) {
    die("Parrain non connecté.");
}

$parrain_id = $_SESSION['parrain_id'];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['enfants']) && is_array($_POST['enfants'])) {
    $stmt = $pdo->prepare("INSERT INTO demande_parrainage (parrain_id, enfant_id, structure_id, statut) VALUES (?, ?, ?, ?)");

    foreach ($_POST['enfants'] as $enfant_id) {
        // Récupérer automatiquement le structure_id de l'enfant
        $infoStmt = $pdo->prepare("SELECT structure_id FROM enfant WHERE id = ?");
        $infoStmt->execute([$enfant_id]);
        $enfant = $infoStmt->fetch(PDO::FETCH_ASSOC);

        if ($enfant) {
            $statut = "en attente"; // définir le statut ici
            $stmt->execute([$parrain_id, $enfant_id, $enfant['structure_id'], $statut]);
        }
    }

    echo "🎉 Vos demandes de parrainage ont été enregistrées avec succès !";
} else {
    echo "Aucun enfant sélectionné.";
} 
?>
