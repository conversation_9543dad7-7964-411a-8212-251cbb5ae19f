<?php
// Connexion à la base de données
$pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');

// Requête pour récupérer les enfants avec leurs relations
$sql = "
    SELECT 
        e.id, 
        e.matricule,
        e.nom,
        e.prenom,
        e.date_naissance,
        e.historique_accueil,
        e.date_enregistrement,
        e.derniere_mise_a_jour,
        s.nom AS sexe,
        st.nom AS statut,
        str.nom AS structure,
        e.photo_portrait,
        e.photo,
        e.handicap,
        p.nom AS nom_parrain
    FROM enfant e
    LEFT JOIN sexe s ON e.sexe_id = s.id
    LEFT JOIN statut_enfant st ON e.statut_enfant_id = st.id
    LEFT JOIN structure str ON e.structure_id = str.id
    LEFT JOIN parrain p ON e.parrain_id = p.id
    ORDER BY e.date_enregistrement DESC
";

$enfants = $pdo->query($sql)->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Liste des enfants enregistrés</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .portrait {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
        }
    </style>
</head>
<body class="bg-light">
<div class="container mt-5">
    <h2 class="mb-4">Liste des enfants enregistrés</h2>
    <div class="d-flex justify-content-end mb-3">
        <a href="ajouter_enfant.php" class="btn btn-success">
            + Ajouter un enfant
        </a>
    </div>
    <table class="table table-bordered table-striped table-hover bg-white shadow">
        <thead class="table-primary">
            <tr>
                <th>Photo</th>
                <th>Matricule</th>
                <th>Nom</th>
                <th>Prénom</th>
                <th>Sexe</th>
                <th>Statut</th>
                <th>Structure</th>
                <th>Date naissance</th>
                <th>Historique</th>
                <th>Handicap</th>
                <th>Parrain</th>
                <th>Photo fichier</th>
                <th>Date enr.</th>
                <th>Dernière MAJ</th>
            </tr>
        </thead>
        <tbody>
        <?php if (count($enfants) > 0): ?>
            <?php foreach ($enfants as $e): ?>
                <tr>
                    <td>
                        <?php if ($e['photo_portrait']): ?>
                            <img src="<?= htmlspecialchars($e['photo_portrait']) ?>" class="portrait" alt="Photo">
                        <?php else: ?>
                            <span class="text-muted">Aucune</span>
                        <?php endif; ?>
                    </td>
                    <td><?= htmlspecialchars($e['matricule']) ?></td>
                    <td><?= htmlspecialchars($e['nom']) ?></td>
                    <td><?= htmlspecialchars($e['prenom']) ?></td>
                    <td><?= htmlspecialchars($e['sexe']) ?></td>
                    <td><?= htmlspecialchars($e['statut']) ?></td>
                    <td><?= htmlspecialchars($e['structure']) ?></td>
                    <td><?= htmlspecialchars($e['date_naissance']) ?></td>
                    <td><?= htmlspecialchars($e['historique_accueil']) ?></td>
                    <td><?= $e['handicap'] ? 'Oui' : 'Non' ?></td>
                    <td><?= htmlspecialchars($e['nom_parrain'] ?? 'Aucun') ?></td>
                    <td><?= htmlspecialchars($e['photo'] ?? '-') ?></td>
                    <td><?= htmlspecialchars($e['date_enregistrement']) ?></td>
                    <td><?= htmlspecialchars($e['derniere_mise_a_jour']) ?></td>
                </tr>
            <?php endforeach; ?>
        <?php else: ?>
            <tr>
                <td colspan="15" class="text-center">Aucun enfant enregistré.</td>
            </tr>
        <?php endif; ?>
        </tbody>
    </table>
</div>
</body>
</html>
