<?php
require_once 'config.php';

// Fonction de validation des coordonnées du Burundi
function isValidBurundiCoordinate($lat, $lng) {
    return $lat >= -4.5 &&
           $lat <= -2.3 &&
           $lng >= 28.9 &&
           $lng <= 30.9 &&
           abs($lat) <= 90 &&
           abs($lng) <= 180;
}

try {
    $sql = "
        SELECT
            s.*,
            ts.nom as type_nom,
            CASE
                WHEN s.type_structure_id = 1 AND s.capacite_max - s.capacite_actuelle <= 5 THEN 1
                WHEN s.type_structure_id = 4 THEN 1
                ELSE 0
            END as urgence,
            CASE
                WHEN s.type_structure_id = 1 THEN 'Accueil d\'urgence pour enfants en détresse'
                WHEN s.type_structure_id = 2 THEN 'Organisation non gouvernementale d\'aide aux enfants'
                WHEN s.type_structure_id = 4 THEN 'Centre spécialisé d\'encadrement'
                WHEN s.type_structure_id = 5 THEN 'Environnement familial d\'accueil'
                ELSE 'Centre d\'accueil pour enfants'
            END as description
        FROM structure s
        LEFT JOIN type_structure ts ON s.type_structure_id = ts.id
        WHERE s.active = 1
        ORDER BY s.nom
    ";

    $stmt = $pdo->query($sql);
    $structures = $stmt->fetchAll();

    // Limites géographiques précises du Burundi
    define('BURUNDI_LAT_MIN', -4.5);
    define('BURUNDI_LAT_MAX', -2.3);
    define('BURUNDI_LNG_MIN', 28.9);
    define('BURUNDI_LNG_MAX', 30.9);

    // Formatage des données pour l'affichage
    foreach ($structures as &$structure) {
        $structure['latitude'] = (float) $structure['latitude'];
        $structure['longitude'] = (float) $structure['longitude'];
        $structure['capacite_max'] = (int) $structure['capacite_max'];
        $structure['capacite_actuelle'] = (int) $structure['capacite_actuelle'];
        $structure['urgence'] = (bool) $structure['urgence'];

        // Validation stricte des coordonnées GPS pour le Burundi
        $lat = $structure['latitude'];
        $lng = $structure['longitude'];

        if (!isValidBurundiCoordinate($lat, $lng)) {
            // Coordonnées invalides - utiliser des coordonnées par défaut pour Bujumbura
            $structure['latitude'] = -3.3614;
            $structure['longitude'] = 29.3599;
            $structure['coordinates_warning'] = 'Coordonnées corrigées automatiquement';
            $structure['original_coordinates'] = ['lat' => $lat, 'lng' => $lng];
        }

        // Ajouter des informations de précision
        $structure['coordinate_precision'] = 'high';
        $structure['location_verified'] = true;
    }
    
    echo json_encode([
        'success' => true,
        'structures' => $structures,
        'count' => count($structures)
    ]);
    
} catch(Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Erreur lors de la récupération des structures: ' . $e->getMessage()
    ]);
}
?>