<?php
session_start();
$pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');

$enfants = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['structure_id'], $_POST['genre'], $_POST['tranche_age'], $_POST['handicap'])) {
    $structure_id = $_POST['structure_id'];
    $genre = $_POST['genre'];
    $tranche = $_POST['tranche_age'];
    $handicap = $_POST['handicap'];

    $age_min = 1; $age_max = 5;
    if ($tranche === "6-12") { $age_min = 6; $age_max = 12; }
    if ($tranche === "12-18") { $age_min = 12; $age_max = 18; }

    $sexe_id = ($genre === "masculin") ? 1 : 2;

    $stmt = $pdo->prepare("SELECT e.id, e.matricule, e.nom, e.prenom, e.date_naissance, e.photo_portrait, e.historique_accueil, e.handicap,
                                  s.nom AS sexe, st.nom AS statut, str.nom AS structure
                           FROM enfant e
                           LEFT JOIN sexe s ON e.sexe_id = s.id
                           LEFT JOIN statut_enfant st ON e.statut_enfant_id = st.id
                           LEFT JOIN structure str ON e.structure_id = str.id
                           WHERE e.structure_id = ? AND e.sexe_id = ? AND e.handicap = ?
                             AND TIMESTAMPDIFF(YEAR, e.date_naissance, CURDATE()) BETWEEN ? AND ?");
    $stmt->execute([$structure_id, $sexe_id, $handicap, $age_min, $age_max]);
    $enfants = $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Liste des enfants filtrés</title>
  <style>
    .container {
      display: flex;
      gap: 20px;
    }

    .liste {
      width: 60%;
    }

    .details {
      width: 40%;
      border-left: 1px solid #ccc;
      padding-left: 20px;
    }

    .enfant-card {
      display: flex;
      align-items: center;
      gap: 15px;
      border: 1px solid #ccc;
      padding: 10px;
      margin-bottom: 10px;
    }

    .selected {
      background-color: #e0ffe0;
    }

    .infos {
      display: flex;
      flex-direction: column;
    }

    .circle-btn {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      border: none;
      background-color: #3498db;
      color: white;
      font-weight: bold;
      font-size: 18px;
      cursor: pointer;
      flex-shrink: 0;
    }

    .circle-btn:hover {
      background-color: #2980b9;
    }
  </style>
</head>
<body>

<h2>Résultats : Enfants correspondant aux critères</h2>

<?php if (empty($enfants)): ?>
  <p style="color:red;">Aucun enfant ne correspond aux critères.</p>
<?php endif; ?>

<form id="formParrainage" method="post" action="traiter_parrainage.php">
  <div class="container">
    <div class="liste">
      <?php foreach ($enfants as $e): ?>
        <div class="enfant-card" id="enfant-<?= $e['id'] ?>">
          <button type="button" class="circle-btn" onclick="selectionnerEnfant(<?= $e['id'] ?>)">✓</button>
          <div class="infos">
            <strong><?= htmlspecialchars($e['prenom']) ?> <?= htmlspecialchars($e['nom']) ?></strong>
            <button type="button" onclick='afficherDetails(<?= json_encode($e) ?>)'>Afficher</button>
          </div>
          <input type="checkbox" name="enfants[]" value="<?= $e['id'] ?>" id="check-<?= $e['id'] ?>" style="display: none;">
        </div>
      <?php endforeach; ?>
    </div>

    <div class="details" id="details">
      <h3>Détails de l'enfant</h3>
      <p>Sélectionnez un enfant pour voir les détails</p>
    </div>
  </div>

  <div style="margin-top: 20px;">
    <button type="submit">Parrainer</button>
  </div>
</form>

<script>
function afficherDetails(enfant) {
  const container = document.getElementById("details");
  const age = new Date().getFullYear() - new Date(enfant.date_naissance).getFullYear();
  container.innerHTML = `
    <h3>Détails de l'enfant</h3>
    <img src="${enfant.photo_portrait}" alt="photo" width="120"><br><br>
    <strong>Matricule:</strong> ${enfant.matricule}<br>
    <strong>Nom:</strong> ${enfant.nom}<br>
    <strong>Prénom:</strong> ${enfant.prenom}<br>
    <strong>Âge:</strong> ${age} ans<br>
    <strong>Sexe:</strong> ${enfant.sexe}<br>
    <strong>Statut:</strong> ${enfant.statut}<br>
    <strong>Structure:</strong> ${enfant.structure}<br>
    <strong>Handicap:</strong> ${enfant.handicap == 1 ? 'Oui' : 'Non'}<br>
    <strong>Historique:</strong> ${enfant.historique_accueil}
  `;
}

function selectionnerEnfant(id) {
  const checkbox = document.getElementById("check-" + id);
  checkbox.checked = !checkbox.checked;

  const card = document.getElementById("enfant-" + id);
  card.classList.toggle("selected");
}
</script>

</body>
</html>
