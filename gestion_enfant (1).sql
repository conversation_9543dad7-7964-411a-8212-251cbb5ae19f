-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 30, 2025 at 09:41 AM
-- Server version: 10.4.27-MariaDB
-- PHP Version: 8.2.0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `gestion_enfant`
--

-- --------------------------------------------------------

--
-- Table structure for table `demande_parrainage`
--

CREATE TABLE `demande_parrainage` (
  `id` int(11) NOT NULL,
  `parrain_id` int(11) NOT NULL,
  `enfant_id` int(11) NOT NULL,
  `structure_id` int(11) NOT NULL,
  `date_demande` datetime DEFAULT current_timestamp(),
  `statut` varchar(50) NOT NULL DEFAULT 'en_attente'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `demande_parrainage`
--

INSERT INTO `demande_parrainage` (`id`, `parrain_id`, `enfant_id`, `structure_id`, `date_demande`, `statut`) VALUES
(1, 3, 8, 2, '2025-07-23 11:01:00', 'valide'),
(2, 3, 9, 2, '2025-07-23 11:01:00', 'valide'),
(3, 3, 8, 2, '2025-07-23 11:10:13', 'valide'),
(4, 3, 9, 2, '2025-07-23 11:10:14', 'valide'),
(5, 6, 10, 3, '2025-07-23 14:44:15', 'en_attente'),
(6, 5, 11, 4, '2025-07-24 14:31:12', 'en_attente'),
(7, 5, 12, 5, '2025-07-24 15:10:59', 'en attente');

-- --------------------------------------------------------

--
-- Table structure for table `donateur`
--

CREATE TABLE `donateur` (
  `id` int(11) NOT NULL,
  `nom` varchar(255) DEFAULT NULL,
  `categorie` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `donation`
--

CREATE TABLE `donation` (
  `id` int(11) NOT NULL,
  `montant` double DEFAULT NULL,
  `devise` varchar(10) DEFAULT NULL,
  `date_donation` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `reference` varchar(100) DEFAULT NULL,
  `anonyme` tinyint(1) DEFAULT 0,
  `type_id` int(11) DEFAULT NULL,
  `structure_id` int(11) DEFAULT NULL,
  `donateur_id` int(11) DEFAULT NULL,
  `mode_paiement_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `enfant`
--

CREATE TABLE `enfant` (
  `id` int(11) NOT NULL,
  `matricule` varchar(20) DEFAULT NULL,
  `nom` varchar(100) DEFAULT NULL,
  `prenom` varchar(100) DEFAULT NULL,
  `date_naissance` date DEFAULT NULL,
  `historique_accueil` enum('malade','handicap','malade mentale','bonne sante') DEFAULT NULL,
  `photo_portrait` varchar(255) DEFAULT NULL,
  `date_enregistrement` date DEFAULT NULL,
  `derniere_mise_a_jour` date DEFAULT NULL,
  `sexe_id` int(11) DEFAULT NULL,
  `structure_id` int(11) DEFAULT NULL,
  `statut_enfant_id` int(11) DEFAULT NULL,
  `photo` varchar(255) DEFAULT NULL,
  `handicap` tinyint(1) DEFAULT 0,
  `parrain_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `enfant`
--

INSERT INTO `enfant` (`id`, `matricule`, `nom`, `prenom`, `date_naissance`, `historique_accueil`, `photo_portrait`, `date_enregistrement`, `derniere_mise_a_jour`, `sexe_id`, `structure_id`, `statut_enfant_id`, `photo`, `handicap`, `parrain_id`) VALUES
(2, NULL, 'guy', 'ad', '2025-07-10', '', NULL, '2025-07-15', '2025-07-15', 2, 5, 2, 'james-daniella.jpg', 0, NULL),
(3, 'M25/000003', 'Niyo', 'Alain', '2025-07-13', 'handicap', 'uploads/687666c38bb1f_Screenshot_20250220-141413.jpg', '2025-07-15', '2025-07-15', 2, 2, 1, NULL, 0, NULL),
(6, 'M25/000004', 'kubu', 'seb', '2024-06-12', 'handicap', 'uploads/6876694cdba37_Screenshot_20250220-141413.jpg', '2025-07-15', '2025-07-15', 1, 3, 1, NULL, 0, NULL),
(8, 'M25/000005', 'kazoza', 'benitha', '2017-06-15', 'handicap', 'uploads/687f5e2e5e238_nii.webp', '2025-07-22', '2025-07-22', 2, 2, 1, NULL, 1, NULL),
(9, 'M25/000006', 'keza', 'bechou', '2017-06-15', 'handicap', 'uploads/687f5e4ab1cbb_nii.webp', '2025-07-22', '2025-07-22', 2, 2, 1, NULL, 1, NULL),
(10, 'M25/000007', 'amani', 'nana', '2021-06-11', 'malade', 'uploads/6880d89b299d5_gettyimages-1352603244-612x612.jpg', '2025-07-23', '2025-07-23', 2, 3, 4, NULL, 1, NULL),
(11, 'M25/000008', 'kaneza', 'fille', '2015-07-13', 'bonne sante', 'uploads/688226dc5c6b6_Screenshot_20230731-220614~2.png', '2025-07-24', '2025-07-24', 2, 4, 2, NULL, 0, NULL),
(12, 'M25/000009', 'ngabo', 'don', '2008-11-03', 'bonne sante', 'uploads/68823077706e5_images.jpg', '2025-07-24', '2025-07-24', 1, 5, 5, NULL, 0, NULL),
(13, 'M25/000010', 'ntwari', 'kelly', '2007-03-14', 'bonne sante', 'uploads/688231a355a26_gettyimages-1352603244-612x612.jpg', '2025-07-24', '2025-07-24', 1, 5, 2, NULL, 0, NULL),
(14, 'M25/000011', 'kazoza', 'mika', '2008-06-12', 'bonne sante', 'uploads/688232019883c_logo.jfif', '2025-07-24', '2025-07-24', 2, 5, 4, NULL, 0, NULL),
(15, 'M25/000012', 'banga', 'channy', '2007-07-19', 'bonne sante', 'uploads/6882326007061_Screenshot_20220916-225021~2 - Copie - Copie.png', '2025-07-24', '2025-07-24', 2, 5, 6, NULL, 0, NULL),
(16, 'M25/000013', 'kabebe', 'sonny', '2019-06-06', 'bonne sante', 'uploads/688232bbd3fff_Screenshot_20221011-120353~2 - Copie.png', '2025-07-24', '2025-07-24', 1, 4, 2, NULL, 0, NULL),
(17, 'M25/000014', 'karire', 'ange', '2018-07-12', 'malade', 'uploads/688233160f5ab_Screenshot_20221011-120353~2 - Copie.png', '2025-07-24', '2025-07-24', 2, 5, 5, NULL, 1, NULL),
(18, 'M25/000015', 'ishimwe', 'sania', '2022-11-21', 'malade mentale', 'uploads/688233d99145f_Screenshot_20221202-225750~2.png', '2025-07-24', '2025-07-24', 2, 3, 3, NULL, 1, NULL),
(19, 'M25/000016', 'iriho', 'princia', '2010-11-16', 'malade mentale', 'uploads/688237a3df927_Screenshot_20221026-085847~2 - Copie.png', '2025-07-24', '2025-07-24', 2, 2, 6, NULL, 1, NULL),
(20, 'M25/000017', 'kamariza', 'diane', '2009-10-27', 'malade mentale', 'uploads/68823804a644b_Screenshot_20230604-161122~2.png', '2025-07-24', '2025-07-24', 2, 3, 6, NULL, 0, NULL),
(21, 'M25/000018', 'ngabo', 'tresor', '2022-07-06', 'bonne sante', 'uploads/68823a3e9f8a5_Screenshot_20221026-085721~2 - Copie (2).png', '2025-07-24', '2025-07-24', 1, 4, 5, NULL, 0, NULL),
(22, 'M25/000019', 'kanje', 'lolo', '2008-03-19', 'bonne sante', 'uploads/68823aa5cec60_nn.jpg', '2025-07-24', '2025-07-24', 2, 2, 3, NULL, 0, NULL),
(23, 'M25/000020', 'kagabo', 'claude', '2009-10-05', 'bonne sante', 'uploads/68823b1cdeb51_Screenshot_20221026-084119~2 - Copie (2).png', '2025-07-24', '2025-07-24', 1, 2, 1, NULL, 0, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `mode_paiement`
--

CREATE TABLE `mode_paiement` (
  `id` int(11) NOT NULL,
  `nom` varchar(30) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `mode_paiement`
--

INSERT INTO `mode_paiement` (`id`, `nom`) VALUES
(2, 'CARTE_BANCAIRE'),
(4, 'ESPECES'),
(1, 'MOBILE_MONEY'),
(3, 'VIREMENT');

-- --------------------------------------------------------

--
-- Table structure for table `parrain`
--

CREATE TABLE `parrain` (
  `id` int(11) NOT NULL,
  `nom` varchar(255) DEFAULT NULL,
  `prenom` varchar(255) DEFAULT NULL,
  `telephone` varchar(30) DEFAULT NULL,
  `adresse` varchar(255) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `numero_cni` varchar(50) DEFAULT NULL,
  `photo_cni` varchar(255) DEFAULT NULL,
  `quartier_residence` varchar(100) DEFAULT NULL,
  `zone` varchar(100) DEFAULT NULL,
  `commune` varchar(100) DEFAULT NULL,
  `province` varchar(100) DEFAULT NULL,
  `statut_familial` varchar(50) DEFAULT NULL,
  `historique_bancaire` text DEFAULT NULL,
  `mot_de_passe` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `parrain`
--

INSERT INTO `parrain` (`id`, `nom`, `prenom`, `telephone`, `adresse`, `email`, `numero_cni`, `photo_cni`, `quartier_residence`, `zone`, `commune`, `province`, `statut_familial`, `historique_bancaire`, `mot_de_passe`) VALUES
(1, NULL, NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, ''),
(2, 'NIYONZIMA', 'Anicet', '65767879', 'kabondo av lac weru no 45', '<EMAIL>', '531/37.678', 'uploads/6878fd8bc1076_22.jpg', 'kabondo', 'rohero', 'ntahangwa', 'bujumbura', 'Parent célibataire', 'uploads/6878fd8bcac4e_CV NISSI[1].pdf', ''),
(3, 'NIYONGABO', 'Bango', '76356273', 'kabondo av lac weru no 45', '<EMAIL>', '531/37.678', 'uploads/687a0d5472c86_55.jpg', 'kabondo', 'rohero', 'ntahangwa', 'bujumbura', 'Marié(e)', 'uploads/687a0d54739f6_CV NISSI[1].pdf', '$2y$10$YQx6dhY5UuJhe1LjlnrPTekWMovLyoLSvEfkK9AdoiSmoXq4OJCkG'),
(4, 'NIYONGABO', 'Bango', '76356273', 'kabondo av lac weru no 45', '<EMAIL>', '531/37.678', 'uploads/687a0fe50773b_55.jpg', 'kabondo', 'rohero', 'ntahangwa', 'bujumbura', 'Marié(e)', 'uploads/687a0fe508299_CV NISSI[1].pdf', '$2y$10$X2VoVqq1jZYbDWN752gFGuhPYgEQYnzAHpXy7cAEWmn2bKUV3uBae'),
(5, 'niyonkuru', 'alain', '76356273', 'kabondo av lac weru no 45', '<EMAIL>', '5367.36427/635', 'uploads/6880d6c4cc5ba_james-daniella.jpg', 'kabondo', 'rohero', 'ntahangwa', 'bujumbura', 'Parent célibataire', 'uploads/6880d6c4d4031_CV NISSI[1].pdf', '$2y$10$wvznNILTofNe4ZxIuWOTvus7fTcpRpFjWeQd7cCBLe/9xaRt5wjOO'),
(6, 'karire', 'ange', '76356273', 'kabondo av lac ruvubu no 3', '<EMAIL>', '2566/372635.367', 'uploads/6880d78e42d75_unnamed.png', 'kinama', 'ngagara', 'ntahangwa', 'bujumbura', 'Marié(e)', 'uploads/6880d78e434b9_tia.png', '$2y$10$ihc2DrvqEzCYze3e9i9xSuiGnXj86.VNBqw5rF8qnE28W8SXgELKe'),
(7, 'IRADUKUNDA', 'Malack', '25762647747', 'kinanira3,bujumbura', '<EMAIL>', '947888.88090909', 'uploads/688345f27603e_james-daniella.jpg', 'kinanira3', 'musaga', 'Muha', 'bujumbura', 'Célibataire', 'uploads/688345f27fe13_Screenshot_20220916-225021~2 - Copie - Copie.png', '$2y$10$K8V5XTZPY558XMSO0sP8VuydeF.MDLU2Qqpcado7jMzrEyOVZFrdS');

-- --------------------------------------------------------

--
-- Table structure for table `parrainage`
--

CREATE TABLE `parrainage` (
  `id` int(11) NOT NULL,
  `montant_mensuel` double DEFAULT NULL,
  `date_debut` datetime DEFAULT NULL,
  `date_fin` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `conditions` text DEFAULT NULL,
  `type` varchar(255) DEFAULT NULL,
  `statut` varchar(255) DEFAULT NULL,
  `enfant_id` int(11) DEFAULT NULL,
  `parrain_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `projet`
--

CREATE TABLE `projet` (
  `id` int(11) NOT NULL,
  `titre` varchar(150) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `objectif_financier` double DEFAULT NULL,
  `montant_collecte` double DEFAULT NULL,
  `date_debut` datetime DEFAULT NULL,
  `date_fin` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `documents_joints` text DEFAULT NULL,
  `structure_id` int(11) DEFAULT NULL,
  `statut_projet_id` int(11) DEFAULT NULL,
  `id_type_structure` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `projet`
--

INSERT INTO `projet` (`id`, `titre`, `description`, `objectif_financier`, `montant_collecte`, `date_debut`, `date_fin`, `documents_joints`, `structure_id`, `statut_projet_id`, `id_type_structure`) VALUES
(1, 'contstruction des salles de classe', 'ggggyyyyjjjjkkkkk', 2.44, 1.07, '2025-07-25 00:00:00', '2026-05-14 22:00:00', 'uploads/6877a58f2d00b.pdf', 4, 2, NULL),
(2, 'contstruction des salles de classe', 'ggggyyyyjjjjkkkkk', 2.44, 1.07, '2025-07-25 00:00:00', '2026-05-14 22:00:00', 'uploads/6877a8ac3bd97.pdf', 4, 2, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `sexe`
--

CREATE TABLE `sexe` (
  `id` int(11) NOT NULL,
  `nom` varchar(10) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `sexe`
--

INSERT INTO `sexe` (`id`, `nom`) VALUES
(2, 'FEMININS'),
(1, 'MASCULIN');

-- --------------------------------------------------------

--
-- Table structure for table `signalement`
--

CREATE TABLE `signalement` (
  `id` int(11) NOT NULL,
  `description` text DEFAULT NULL,
  `latitude` double DEFAULT NULL,
  `longitude` double DEFAULT NULL,
  `adresse` text DEFAULT NULL,
  `date_signalement` datetime DEFAULT NULL,
  `date_traitement` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `photo_enfant` text DEFAULT NULL,
  `age_estime` int(11) DEFAULT NULL,
  `type_id` int(11) DEFAULT NULL,
  `statut_id` int(11) DEFAULT NULL,
  `signalant_id` int(11) DEFAULT NULL,
  `structure_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `statistique`
--

CREATE TABLE `statistique` (
  `id` int(11) NOT NULL,
  `periode` varchar(50) DEFAULT NULL,
  `region` varchar(100) DEFAULT NULL,
  `donnees` varchar(100) DEFAULT NULL,
  `date_generation` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `type_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `statut_enfant`
--

CREATE TABLE `statut_enfant` (
  `id` int(11) NOT NULL,
  `nom` varchar(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `statut_enfant`
--

INSERT INTO `statut_enfant` (`id`, `nom`) VALUES
(5, 'ADOPTE'),
(1, 'EN_ACCUEIL'),
(4, 'EN_DANGER'),
(6, 'RECHERCHE'),
(2, 'REINSERE'),
(3, 'SCOLARISE');

-- --------------------------------------------------------

--
-- Table structure for table `statut_parrainage`
--

CREATE TABLE `statut_parrainage` (
  `id` int(11) NOT NULL,
  `nom` varchar(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `statut_parrainage`
--

INSERT INTO `statut_parrainage` (`id`, `nom`) VALUES
(1, 'ACTIFS'),
(4, 'EN_ATTENTE'),
(2, 'SUSPENDUS'),
(3, 'TERMINER');

-- --------------------------------------------------------

--
-- Table structure for table `statut_projet`
--

CREATE TABLE `statut_projet` (
  `id` int(11) NOT NULL,
  `nom` varchar(30) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `statut_projet`
--

INSERT INTO `statut_projet` (`id`, `nom`) VALUES
(2, 'ACTIFS'),
(1, 'PLANIFIER'),
(4, 'SUSPENDU'),
(3, 'TERMINE');

-- --------------------------------------------------------

--
-- Table structure for table `statut_signalement`
--

CREATE TABLE `statut_signalement` (
  `id` int(11) NOT NULL,
  `nom` varchar(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `statut_signalement`
--

INSERT INTO `statut_signalement` (`id`, `nom`) VALUES
(2, 'EN_COURS'),
(4, 'FERMER'),
(1, 'NOUVEAU'),
(3, 'TRAITE'),
(5, 'URGENT');

-- --------------------------------------------------------

--
-- Table structure for table `structure`
--

CREATE TABLE `structure` (
  `id` int(11) NOT NULL,
  `nom` varchar(150) DEFAULT NULL,
  `adresse` text DEFAULT NULL,
  `latitude` double DEFAULT NULL,
  `longitude` double DEFAULT NULL,
  `capacite_max` int(11) DEFAULT NULL,
  `capacite_actuelle` int(11) DEFAULT NULL,
  `telephone` varchar(20) DEFAULT NULL,
  `email` varchar(150) DEFAULT NULL,
  `responsable` varchar(100) DEFAULT NULL,
  `active` tinyint(1) DEFAULT 1,
  `date_creation` timestamp NOT NULL DEFAULT current_timestamp(),
  `type_structure_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `structure`
--

INSERT INTO `structure` (`id`, `nom`, `adresse`, `latitude`, `longitude`, `capacite_max`, `capacite_actuelle`, `telephone`, `email`, `responsable`, `active`, `date_creation`, `type_structure_id`) VALUES
(2, 'orphelinat igikundiro', 'jhqwe', 25, 26, 42, 39, '5267i82', '<EMAIL>', 'so vivi', 1, '2025-07-14 14:26:54', 1),
(3, ' orphelinat ejo heza', 'kanyosha 3ieme avenue', 232, 238, 33, 52, '795674666', '<EMAIL>', 'soeur divine', 1, '2025-07-15 07:48:08', 1),
(4, 'orphelinat  KIRA', 'kirundo ', 621, 1245, 265, 367, '76356273', '<EMAIL>', 'karubwenge', 1, '2025-07-15 07:52:51', 1),
(5, 'orphelinat uzobaho', 'mwaro', 23, 36, 27, 21, '668732689', '<EMAIL>', 'karorero', 1, '2025-07-15 07:55:11', 1);

-- --------------------------------------------------------

--
-- Table structure for table `suivi_enfant`
--

CREATE TABLE `suivi_enfant` (
  `id` int(11) NOT NULL,
  `enfant_id` int(11) DEFAULT NULL,
  `type_id` int(11) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `observations` text DEFAULT NULL,
  `responsable_id` int(11) DEFAULT NULL,
  `date_suivi` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `documents_joints` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `type_donation`
--

CREATE TABLE `type_donation` (
  `id` int(11) NOT NULL,
  `nom` varchar(30) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `type_donation`
--

INSERT INTO `type_donation` (`id`, `nom`) VALUES
(2, 'MENSUELLE'),
(1, 'PONCTUELLE'),
(3, 'PROJET SPECIFIQUE'),
(4, 'URGENCE');

-- --------------------------------------------------------

--
-- Table structure for table `type_parrainage`
--

CREATE TABLE `type_parrainage` (
  `id` int(11) NOT NULL,
  `nom` varchar(30) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `type_parrainage`
--

INSERT INTO `type_parrainage` (`id`, `nom`) VALUES
(2, 'COLLECTIFS'),
(3, 'EDUCATIFS'),
(1, 'INDIVIDUEL'),
(4, 'MEDICAL'),
(5, 'PROJET_STRUCTURE');

-- --------------------------------------------------------

--
-- Table structure for table `type_signalement`
--

CREATE TABLE `type_signalement` (
  `id` int(11) NOT NULL,
  `nom` varchar(30) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `type_signalement`
--

INSERT INTO `type_signalement` (`id`, `nom`) VALUES
(4, 'ABANDON'),
(2, 'ENFANT_PERDUS'),
(1, 'ENFANT_RUE'),
(3, 'MALTRAITANCE'),
(5, 'URGENCE_MEDICAL');

-- --------------------------------------------------------

--
-- Table structure for table `type_statistique`
--

CREATE TABLE `type_statistique` (
  `id` int(11) NOT NULL,
  `nom` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `type_statistique`
--

INSERT INTO `type_statistique` (`id`, `nom`) VALUES
(4, 'DONATION'),
(1, 'ENFANT_ACCUEILLIS'),
(5, 'PARRAINAGE'),
(2, 'REINSERTION'),
(3, 'SIGNALEMENT'),
(6, 'STRUCTURE_ACTIVES');

-- --------------------------------------------------------

--
-- Table structure for table `type_structure`
--

CREATE TABLE `type_structure` (
  `id` int(11) NOT NULL,
  `nom` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `type_structure`
--

INSERT INTO `type_structure` (`id`, `nom`) VALUES
(4, 'CENTRE_ENCADREMENT'),
(5, 'FAMILLE_ACCUEIL'),
(2, 'ONG'),
(1, 'ORPHELINAT');

-- --------------------------------------------------------

--
-- Table structure for table `type_suivi`
--

CREATE TABLE `type_suivi` (
  `id` int(11) NOT NULL,
  `nom` varchar(30) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `type_suivi`
--

INSERT INTO `type_suivi` (`id`, `nom`) VALUES
(2, 'EDUCATIF'),
(6, 'FAMILIAL'),
(5, 'JURIDIQUE'),
(1, 'MEDICAL'),
(3, 'PSYCOLOGIQUE'),
(4, 'SOCIAL');

-- --------------------------------------------------------

--
-- Table structure for table `type_utilisateur`
--

CREATE TABLE `type_utilisateur` (
  `id` int(11) NOT NULL,
  `nom` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `type_utilisateur`
--

INSERT INTO `type_utilisateur` (`id`, `nom`) VALUES
(4, 'ASSISTANT_CENTRE'),
(6, 'MINISTERE'),
(3, 'ONG_ACCOMPAGNEMENT'),
(7, 'PARTENAIRE_FINANCIER'),
(1, 'RESPONSABLE_CENTRE');

-- --------------------------------------------------------

--
-- Table structure for table `utilisateur`
--

CREATE TABLE `utilisateur` (
  `id` int(11) NOT NULL,
  `nom` varchar(100) DEFAULT NULL,
  `prenom` varchar(100) DEFAULT NULL,
  `email` varchar(150) DEFAULT NULL,
  `telephone` varchar(20) DEFAULT NULL,
  `mot_de_passe` text DEFAULT NULL,
  `actif` tinyint(1) DEFAULT 1,
  `date_creation` timestamp NOT NULL DEFAULT current_timestamp(),
  `derniere_connexion` datetime DEFAULT NULL,
  `type_utilisateur_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `utilisateur`
--

INSERT INTO `utilisateur` (`id`, `nom`, `prenom`, `email`, `telephone`, `mot_de_passe`, `actif`, `date_creation`, `derniere_connexion`, `type_utilisateur_id`) VALUES
(3, 'tes', 'test', '<EMAIL>', '67885473', '$2y$10$h0iYlsUWtUgm7wxSF1xdnO8bZB2VgvHfNc3reWKLFOvcqzfN3/Vwu', 1, '2025-07-10 12:59:45', NULL, 3),
(5, 'adi', 'guy', '<EMAIL>', '67885473', '$2y$10$i1gwBzMoNaCE8SUqPMZUkest6ZXIivXVgodlEJV2rnAKrLrXJxkKC', 1, '2025-07-10 13:26:25', NULL, 6),
(6, 'ig', 'ad', '<EMAIL>', '67885473', '$2y$10$CS9BCntC765YDmUidL5fyeAHAoPxb262UGtQFXMKQafdccjJxkPf2', 1, '2025-07-10 13:38:06', NULL, 1),
(7, 'kana', 'vava', '<EMAIL>', '67898765', '$2y$10$NT2TS4y1DxOFRGP6uhXTpuqgyMH21Nn/vNROa.lbBxe2HUuUx.w9a', 1, '2025-07-28 12:51:51', NULL, 3);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `demande_parrainage`
--
ALTER TABLE `demande_parrainage`
  ADD PRIMARY KEY (`id`),
  ADD KEY `parrain_id` (`parrain_id`),
  ADD KEY `enfant_id` (`enfant_id`),
  ADD KEY `structure_id` (`structure_id`);

--
-- Indexes for table `donateur`
--
ALTER TABLE `donateur`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `donation`
--
ALTER TABLE `donation`
  ADD PRIMARY KEY (`id`),
  ADD KEY `ccd` (`donateur_id`),
  ADD KEY `cmpc` (`mode_paiement_id`),
  ADD KEY `ctd` (`type_id`);

--
-- Indexes for table `enfant`
--
ALTER TABLE `enfant`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `matricule` (`matricule`),
  ADD KEY `ces` (`sexe_id`),
  ADD KEY `sdc` (`statut_enfant_id`),
  ADD KEY `esc` (`structure_id`),
  ADD KEY `fk_parrain_enfant` (`parrain_id`);

--
-- Indexes for table `mode_paiement`
--
ALTER TABLE `mode_paiement`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `nom` (`nom`);

--
-- Indexes for table `parrain`
--
ALTER TABLE `parrain`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `parrainage`
--
ALTER TABLE `parrainage`
  ADD PRIMARY KEY (`id`),
  ADD KEY `ce` (`enfant_id`),
  ADD KEY `cpp` (`parrain_id`);

--
-- Indexes for table `projet`
--
ALTER TABLE `projet`
  ADD PRIMARY KEY (`id`),
  ADD KEY `csp` (`structure_id`),
  ADD KEY `cstp` (`statut_projet_id`),
  ADD KEY `fk_projet_type_structure` (`id_type_structure`);

--
-- Indexes for table `sexe`
--
ALTER TABLE `sexe`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `nom` (`nom`);

--
-- Indexes for table `signalement`
--
ALTER TABLE `signalement`
  ADD PRIMARY KEY (`id`),
  ADD KEY `cstt` (`type_id`),
  ADD KEY `csttt` (`statut_id`),
  ADD KEY `css` (`signalant_id`),
  ADD KEY `cstct` (`structure_id`);

--
-- Indexes for table `statistique`
--
ALTER TABLE `statistique`
  ADD PRIMARY KEY (`id`),
  ADD KEY `cst` (`type_id`);

--
-- Indexes for table `statut_enfant`
--
ALTER TABLE `statut_enfant`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `nom` (`nom`);

--
-- Indexes for table `statut_parrainage`
--
ALTER TABLE `statut_parrainage`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `nom` (`nom`);

--
-- Indexes for table `statut_projet`
--
ALTER TABLE `statut_projet`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `nom` (`nom`);

--
-- Indexes for table `statut_signalement`
--
ALTER TABLE `statut_signalement`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `nom` (`nom`);

--
-- Indexes for table `structure`
--
ALTER TABLE `structure`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_type_structure` (`type_structure_id`);

--
-- Indexes for table `suivi_enfant`
--
ALTER TABLE `suivi_enfant`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `type_donation`
--
ALTER TABLE `type_donation`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `nom` (`nom`);

--
-- Indexes for table `type_parrainage`
--
ALTER TABLE `type_parrainage`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `nom` (`nom`);

--
-- Indexes for table `type_signalement`
--
ALTER TABLE `type_signalement`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `nom` (`nom`);

--
-- Indexes for table `type_statistique`
--
ALTER TABLE `type_statistique`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `nom` (`nom`);

--
-- Indexes for table `type_structure`
--
ALTER TABLE `type_structure`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `nom` (`nom`);

--
-- Indexes for table `type_suivi`
--
ALTER TABLE `type_suivi`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `nom` (`nom`);

--
-- Indexes for table `type_utilisateur`
--
ALTER TABLE `type_utilisateur`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `nom` (`nom`);

--
-- Indexes for table `utilisateur`
--
ALTER TABLE `utilisateur`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `ctu` (`type_utilisateur_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `demande_parrainage`
--
ALTER TABLE `demande_parrainage`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `donateur`
--
ALTER TABLE `donateur`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `donation`
--
ALTER TABLE `donation`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `enfant`
--
ALTER TABLE `enfant`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- AUTO_INCREMENT for table `mode_paiement`
--
ALTER TABLE `mode_paiement`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `parrain`
--
ALTER TABLE `parrain`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `parrainage`
--
ALTER TABLE `parrainage`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `projet`
--
ALTER TABLE `projet`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `sexe`
--
ALTER TABLE `sexe`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `signalement`
--
ALTER TABLE `signalement`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `statistique`
--
ALTER TABLE `statistique`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `statut_enfant`
--
ALTER TABLE `statut_enfant`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `statut_parrainage`
--
ALTER TABLE `statut_parrainage`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `statut_projet`
--
ALTER TABLE `statut_projet`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `statut_signalement`
--
ALTER TABLE `statut_signalement`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `structure`
--
ALTER TABLE `structure`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `suivi_enfant`
--
ALTER TABLE `suivi_enfant`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `type_donation`
--
ALTER TABLE `type_donation`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `type_parrainage`
--
ALTER TABLE `type_parrainage`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `type_signalement`
--
ALTER TABLE `type_signalement`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `type_statistique`
--
ALTER TABLE `type_statistique`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `type_structure`
--
ALTER TABLE `type_structure`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `type_suivi`
--
ALTER TABLE `type_suivi`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `type_utilisateur`
--
ALTER TABLE `type_utilisateur`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `utilisateur`
--
ALTER TABLE `utilisateur`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `demande_parrainage`
--
ALTER TABLE `demande_parrainage`
  ADD CONSTRAINT `demande_parrainage_ibfk_1` FOREIGN KEY (`parrain_id`) REFERENCES `parrain` (`id`),
  ADD CONSTRAINT `demande_parrainage_ibfk_2` FOREIGN KEY (`enfant_id`) REFERENCES `enfant` (`id`),
  ADD CONSTRAINT `demande_parrainage_ibfk_3` FOREIGN KEY (`structure_id`) REFERENCES `structure` (`id`);

--
-- Constraints for table `donation`
--
ALTER TABLE `donation`
  ADD CONSTRAINT `ccd` FOREIGN KEY (`donateur_id`) REFERENCES `donateur` (`id`),
  ADD CONSTRAINT `cmpc` FOREIGN KEY (`mode_paiement_id`) REFERENCES `mode_paiement` (`id`),
  ADD CONSTRAINT `csd` FOREIGN KEY (`type_id`) REFERENCES `type_donation` (`id`),
  ADD CONSTRAINT `ctd` FOREIGN KEY (`type_id`) REFERENCES `type_donation` (`id`);

--
-- Constraints for table `enfant`
--
ALTER TABLE `enfant`
  ADD CONSTRAINT `ces` FOREIGN KEY (`sexe_id`) REFERENCES `sexe` (`id`),
  ADD CONSTRAINT `esc` FOREIGN KEY (`structure_id`) REFERENCES `structure` (`id`),
  ADD CONSTRAINT `fk_parrain_enfant` FOREIGN KEY (`parrain_id`) REFERENCES `parrain` (`id`),
  ADD CONSTRAINT `sdc` FOREIGN KEY (`statut_enfant_id`) REFERENCES `statut_enfant` (`id`);

--
-- Constraints for table `parrainage`
--
ALTER TABLE `parrainage`
  ADD CONSTRAINT `ce` FOREIGN KEY (`enfant_id`) REFERENCES `enfant` (`id`),
  ADD CONSTRAINT `cpp` FOREIGN KEY (`parrain_id`) REFERENCES `parrain` (`id`);

--
-- Constraints for table `projet`
--
ALTER TABLE `projet`
  ADD CONSTRAINT `csp` FOREIGN KEY (`structure_id`) REFERENCES `structure` (`id`),
  ADD CONSTRAINT `cstp` FOREIGN KEY (`statut_projet_id`) REFERENCES `statut_projet` (`id`),
  ADD CONSTRAINT `fk_projet_type_structure` FOREIGN KEY (`id_type_structure`) REFERENCES `type_structure` (`id`);

--
-- Constraints for table `signalement`
--
ALTER TABLE `signalement`
  ADD CONSTRAINT `css` FOREIGN KEY (`signalant_id`) REFERENCES `utilisateur` (`id`),
  ADD CONSTRAINT `cstct` FOREIGN KEY (`structure_id`) REFERENCES `structure` (`id`),
  ADD CONSTRAINT `cstt` FOREIGN KEY (`type_id`) REFERENCES `type_signalement` (`id`),
  ADD CONSTRAINT `csttt` FOREIGN KEY (`statut_id`) REFERENCES `statut_signalement` (`id`);

--
-- Constraints for table `statistique`
--
ALTER TABLE `statistique`
  ADD CONSTRAINT `cst` FOREIGN KEY (`type_id`) REFERENCES `type_statistique` (`id`);

--
-- Constraints for table `structure`
--
ALTER TABLE `structure`
  ADD CONSTRAINT `fk_type_structure` FOREIGN KEY (`type_structure_id`) REFERENCES `type_structure` (`id`);

--
-- Constraints for table `utilisateur`
--
ALTER TABLE `utilisateur`
  ADD CONSTRAINT `ctu` FOREIGN KEY (`type_utilisateur_id`) REFERENCES `type_utilisateur` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
