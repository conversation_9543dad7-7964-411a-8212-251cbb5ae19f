<?php
session_start(); // Ajouté pour utiliser $_SESSION

// Configuration de la base de données
$host = 'localhost';        // Adresse du serveur MySQL
$db_name = 'gestion_enfant'; // Nom de la base de données
$username = 'root'; // Ton nom d'utilisateur MySQL
$password = ''; // Ton mot de passe MySQL

try {
    // Connexion à la base de données avec PDO
    $pdo = new PDO("mysql:host=$host;dbname=$db_name;charset=utf8", $username, $password);
    // Définir le mode d'erreur de PDO sur Exception
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    // En cas d'erreur de connexion
    die("Erreur de connexion à la base de données : " . $e->getMessage());
}

$message = "";

if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST['submit'])) {
    $email = trim($_POST["email"]);
    $password = $_POST["password"];

    // Récupération de l'utilisateur avec le type d'utilisateur via une jointure
    $sql = "SELECT u.id, u.email, u.mot_de_passe, t.nom 
            FROM utilisateur u
            JOIN type_utilisateur t ON u.type_utilisateur_id = t.id
            WHERE u.email = :email";

    $stmt = $pdo->prepare($sql);
    $stmt->execute(['email' => $email]);
    $user = $stmt->fetch();

    if ($user) {
        if (password_verify($password, $user['mot_de_passe'])) {
            // Connexion réussie
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['type_utilisateur'] = $user['nom']; // rôle réel

            // Redirection selon le rôle
            switch (trim($user['nom'])) {
                case 'MINISTERE':
                    header("Location: dashboard.php");
                    exit;
                case 'RESPONSABLE_CENTRE':
                    header("Location: gestion centres.php");
                    exit;
                case 'ASSISTANT_SOCIAL ':
                    header("Location: suivi_enfant.php");
                    exit;
                case 'ONG_ACCOMPAGNEMENT':
                    header("Location: dashboard.php");
                    exit;
                case 'visitor':
                    header("Location: visitor_dashboard.php");
                    exit;
                default:
                    $message = "Rôle inconnu.";
            }
            exit();
        } else {
            $message = "Mot de passe incorrect.";
        }
    } else {
        $message = "Email introuvable.";
    }
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - UmwanaVoice</title>
    <!-- Lien vers Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">

<div class="container mt-5">
    <h2 class="mb-4">Connexion</h2>
    <?php if (!empty($message)): ?>
        <div class="alert alert-danger"><?php echo $message; ?></div>
    <?php endif; ?>
    <form action="login.php" method="POST">
        <!-- Email -->
        <div class="mb-3">
            <label for="email" class="form-label">Email :</label>
            <input type="email" id="email" name="email" class="form-control" placeholder="Entrez votre email" required>
        </div>
        <!-- Mot de passe -->
        <div class="mb-3">
            <label for="password" class="form-label">Mot de passe :</label>
            <input type="password" id="password" name="password" class="form-control" placeholder="Entrez votre mot de passe" required>
        </div>
        <!-- Bouton de connexion -->
        <div class="d-grid">
            <button type="submit" name="submit" class="btn btn-primary">Se connecter</button>
        </div>
    </form>
</div>

<!-- Lien vers Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- Scripts de géolocalisation automatique pour préparation post-connexion -->
<script src="js/smart-geolocation.js"></script>
<script src="js/auto-location-manager.js"></script>
<script src="js/auto-init.js"></script>

<script>
// Préparation géolocalisation post-connexion
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔐 Page de connexion - préparation géolocalisation...');

    // Écouter la soumission du formulaire de connexion
    const loginForm = document.querySelector('form');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            console.log('📝 Formulaire de connexion soumis - préparation géolocalisation...');

            // Marquer que l'utilisateur vient de se connecter
            sessionStorage.setItem('umwana_just_logged_in', 'true');
            sessionStorage.setItem('umwana_login_timestamp', Date.now().toString());
        });
    }

    // Si on arrive sur cette page après une déconnexion, nettoyer les données
    sessionStorage.removeItem('umwana_just_logged_in');
    sessionStorage.removeItem('umwana_login_timestamp');
});
</script>

</body>
</html>