<?php
// Connexion à la base de données
try {
    $pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');
} catch (PDOException $e) {
    die("Erreur : " . $e->getMessage());
}

// Récupérer tous les types de structures
$types = $pdo->query("SELECT id, nom FROM type_structure")->fetchAll(PDO::FETCH_ASSOC);

// Traitement du formulaire
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $nom = $_POST['nom'];
    $adresse = $_POST['adresse'];
    $longitude = $_POST['longitude'];
    $latitude = $_POST['latitude'];
    $capacite_max = $_POST['capacite_max'];
    $capacite_actuelle = $_POST['capacite_actuelle'];
    $email = $_POST['email'];
    $telephone = $_POST['telephone'];
    $responsable = $_POST['responsable'];
    $type_id = $_POST['type_structure_id'];

    $sql = "INSERT INTO structure (
                nom, adresse, longitude, latitude,
                capacite_max, capacite_actuelle, email,
                telephone, responsable, type_structure_id, date_creation
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        $nom, $adresse, $longitude, $latitude,
        $capacite_max, $capacite_actuelle, $email,
        $telephone, $responsable, $type_id
    ]);

    header("Location: orphelinats_gestion.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Ajouter une Structure</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f2f4f8;
            padding: 30px;
            color: #2c3e50;
        }

        h1 {
            text-align: center;
            color: #1e3d59;
            margin-bottom: 30px;
        }

        form {
            max-width: 700px;
            margin: auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        label {
            display: block;
            margin-bottom: 6px;
            font-weight: 600;
        }

        input, textarea, select {
            width: 100%;
            padding: 10px;
            margin-bottom: 20px;
            border: 1px solid #ccc;
            border-radius: 6px;
        }

        button {
            background-color: #2ecc71;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            font-weight: bold;
            cursor: pointer;
        }

        button:hover {
            background-color: #27ae60;
        }

        .btn-retour {
            display: inline-block;
            margin-top: 20px;
            color: #1e3d59;
            text-decoration: none;
        }
    </style>
</head>
<body>

<h1>Ajouter une Structure</h1>

<form method="POST" action="">
    <label for="nom">Nom de la structure</label>
    <input type="text" name="nom" id="nom" required>

    <label for="adresse">Adresse</label>
    <textarea name="adresse" id="adresse" rows="3" required></textarea>

    <label for="longitude">Longitude</label>
    <input type="text" name="longitude" id="longitude" required>

    <label for="latitude">Latitude</label>
    <input type="text" name="latitude" id="latitude" required>

    <label for="capacite_max">Capacité maximale</label>
    <input type="number" name="capacite_max" id="capacite_max" required>

    <label for="capacite_actuelle">Capacité actuelle</label>
    <input type="number" name="capacite_actuelle" id="capacite_actuelle" required>

    <label for="email">Email</label>
    <input type="email" name="email" id="email" required>

    <label for="telephone">Téléphone</label>
    <input type="text" name="telephone" id="telephone" required>

    <label for="responsable">Nom du Responsable</label>
    <input type="text" name="responsable" id="responsable" required>

    <label for="type_structure_id">Type de structure</label>
    <select name="type_structure_id" id="type_structure_id" required>
        <option value="">-- Sélectionner un type --</option>
        <?php foreach ($types as $type): ?>
            <option value="<?= htmlspecialchars($type['id']) ?>">
                <?= htmlspecialchars($type['nom']) ?>
            </option>
        <?php endforeach; ?>
    </select>

    <button type="submit">Ajouter</button>
</form>

<div style="text-align: center;">
    <a href="orphelinats_gestion.php" class="btn-retour">← Retour à la liste</a>
</div>

</body>
</html>
