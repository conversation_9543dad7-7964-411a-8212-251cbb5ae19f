<?php
session_start();
$pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');

// Récupération des orphelinats avec l'adresse
$structures = $pdo->query("SELECT id, nom, adresse FROM structure WHERE type_structure_id = 1")->fetchAll(PDO::FETCH_ASSOC);

$message = "";
$enfants = []; 

// Soumission finale de parrainage
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST['enfant_id'])) {
    $stmt = $pdo->prepare("INSERT INTO demande_parrainage (parrain_id, enfant_id, structure_id,statut) VALUES (?, ?, ?,?)");
    $stmt->execute([
        $_SESSION['parrain_id'],
        $_POST['enfant_id'],
        $_POST['structure_id'],
        'en_attente'  // ✅ on définit ici la valeur par défaut du statut
    ]);

    $message = "🎉 Demande enregistrée avec succès. En attente de validation.";
}

// Filtrage après les 4 étapes
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST['structure_id'], $_POST['genre'], $_POST['tranche_age'], $_POST['handicap'])) {
    $structure_id = $_POST['structure_id'];
    $genre = $_POST['genre'];
    $tranche = $_POST['tranche_age'];
    $handicap = $_POST['handicap'];

    $age_min = 1; $age_max = 5;
    if ($tranche === "6-12") { $age_min = 6; $age_max = 12; }
    if ($tranche === "12-18") { $age_min = 12; $age_max = 18; }

    $sexe_id = ($genre === "masculin") ? 1 : 2;

    $stmt = $pdo->prepare("SELECT id, nom, prenom, date_naissance FROM enfant 
        WHERE structure_id = ? AND sexe_id = ? AND handicap = ? 
        AND TIMESTAMPDIFF(YEAR, date_naissance, CURDATE()) BETWEEN ? AND ?");
    $stmt->execute([$structure_id, $sexe_id, $handicap, $age_min, $age_max]);
    $enfants = $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Formulaire de Parrainage</title>
  <style>
    .step { display: none; }
    .step.active { display: block; margin-bottom: 20px; }
    .btn { margin-top: 10px; padding: 8px 16px; }
  </style>
</head>
<body>

<h2>Formulaire de Parrainage</h2>
<?php if ($message): ?>
  <p style="color: green;"><strong><?= $message ?></strong></p>
<?php endif; ?>

<form method="POST" id="form">
  <!-- Étape 1 -->
  <div class="step active" id="step1">
    <h3>1. Dans quel orphelinat ?</h3>
    <?php foreach ($structures as $s): ?>
      <label>
        <input type="radio" name="structure_id" value="<?= $s['id'] ?>" required>
        <?= htmlspecialchars($s['nom']) ?> — <?= htmlspecialchars($s['adresse']) ?> 
      </label><br>
    <?php endforeach; ?>
    <button type="button" class="btn" onclick="nextStep(2)">Suivant</button>
  </div>

  <!-- Étape 2 -->
  <div class="step" id="step2">
    <h3>2. Genre de l'enfant</h3>
    <label><input type="radio" name="genre" value="masculin" required> Masculin</label><br>
    <label><input type="radio" name="genre" value="feminin"> Féminin</label><br>
    <button type="button" class="btn" onclick="nextStep(3)">Suivant</button>
  </div>

  <!-- Étape 3 -->
  <div class="step" id="step3">
    <h3>3. Tranche d'âge</h3>
    <label><input type="radio" name="tranche_age" value="1-5" required> 1-5 ans</label><br>
    <label><input type="radio" name="tranche_age" value="6-12"> 6-12 ans</label><br>
    <label><input type="radio" name="tranche_age" value="12-18"> 12-18 ans</label><br>
    <button type="button" class="btn" onclick="nextStep(4)">Suivant</button>
  </div>

  <!-- Étape 4 -->
  <div class="step" id="step4">
    <h3>4. Enfant en situation de handicap ?</h3>
    <label><input type="radio" name="handicap" value="1" required> Oui</label><br>
    <label><input type="radio" name="handicap" value="0"> Non</label><br>
   <button type="submit" class="btn" formaction="liste_enfants_selection.php">Afficher les enfants</button>
  </div>

  <!-- Étape 5 : Liste des enfants -->
  <?php if (!empty($enfants)): ?>
    <div class="step active" id="step5">
      <h3>Enfants correspondants :</h3>
      <?php foreach ($enfants as $e): ?>
        <label>
          <input type="radio" name="enfant_id" value="<?= $e['id'] ?>" required>
          <?= htmlspecialchars($e['prenom'].' '.$e['nom']) ?> — Né(e) le <?= $e['date_naissance'] ?>
        </label><br>
      <?php endforeach; ?>
      <!-- On garde la structure_id pour l'enregistrement -->
      <input type="hidden" name="structure_id" value="<?= htmlspecialchars($_POST['structure_id']) ?>">
      <br><button type="submit" class="btn">Parrainer</button>
    </div>
  <?php elseif ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST['handicap'])): ?>
    <p style="color:red;">Aucun enfant trouvé avec ces critères.</p>
  <?php endif; ?>
</form>

<script>
function nextStep(stepNumber) {
  document.querySelectorAll('.step').forEach(step => step.classList.remove('active'));
  document.getElementById('step' + stepNumber).classList.add('active');
}
</script>

</body>
</html>
