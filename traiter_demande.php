<?php
$pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['demande_id'], $_POST['action'])) {
    $id = $_POST['demande_id'];
    $action = $_POST['action'];

    if ($action === 'valide' || $action === 'refuse') {
        $stmt = $pdo->prepare("UPDATE demande_parrainage SET statut = ? WHERE id = ?");
        $stmt->execute([$action, $id]);
    }

    header("Location: gestion_demandes.php");
    exit;
}
?>
