<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Gestion des Orphelinats</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Poppins', sans-serif;
      background-color: #f8f9fb;
      color: #2c3e50;
    }

    header {
      background-color: #1e3d59;
      color: white;
      padding: 20px 40px;
      text-align: center;
    }

    header h1 {
      font-size: 26px;
    }

    .container {
      padding: 40px 20px;
      max-width: 1100px;
      margin: auto;
    }

    .title-section {
      text-align: center;
      font-size: 24px;
      margin-bottom: 30px;
      font-weight: 600;
      color: #1e3d59;
    }

    .grid {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 30px;
    }

    .card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      width: 260px;
      padding: 25px;
      text-align: center;
      transition: transform 0.3s ease;
    }

    .card:hover {
      transform: translateY(-5px);
    }

    .card h3 {
      margin-bottom: 15px;
      color: #1e3d59;
      font-size: 20px;
    }

    .card p {
      font-size: 14px;
      color: #555;
      margin-bottom: 20px;
    }

    .btn-acces {
      display: inline-block;
      padding: 10px 18px;
      background-color: #1e3d59;
      color: white;
      border-radius: 6px;
      font-size: 14px;
      font-weight: bold;
      text-decoration: none;
      transition: background-color 0.3s;
    }

    .btn-acces:hover {
      background-color: #163146;
    }

    @media screen and (max-width: 768px) {
      .grid {
        flex-direction: column;
        align-items: center;
      }

      .card {
        width: 90%;
      }
    }
  </style>
</head>
<body>

<header>
  <h1>Interface de Gestion des Orphelinats</h1>
</header>

<div class="container">
  <div class="title-section">Choisissez une action à effectuer</div>

  <div class="grid">

    <!-- Action 1 : Gestion des Orphelinats -->
    <div class="card">
      <h3>Gestion des Orphelinats</h3>
      <p>Ajoutez, modifiez ou supprimez les orphelinats enregistrés.</p>
      <a href="orphelinats_gestion.php" class="btn-acces">Accéder</a>
    </div>

    <!-- Action 2 : Gestion des Enfants -->
    <div class="card">
      <h3>Gestion des Enfants</h3>
      <p>Gérez les enfants rattachés à chaque orphelinat.</p>
      <a href="gestion_enfant.php" class="btn-acces">Accéder</a>
    </div>

    <!-- Action 3 : Gestion des Projets -->
    <div class="card">
      <h3>Gestion des Projets</h3>
      <p>Ajoutez et suivez  les projets liés aux orphelinats.</p>
      <a href="gestion_projet.php" class="btn-acces">Accéder</a>
    </div>

    <!-- Action 4 : Gestion des Parrainages -->
    <div class="card">
      <h3>Gestion des Parrainages</h3>
      <p>Suivez les parrainages et les relations de soutien.</p>
      <a href="parrainages_gestion.php" class="btn-acces">Accéder</a>
    </div>

  </div>
</div>

</body>
</html>
