<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aide aux Enfants de la Rue - Trouver de l'Aide</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <!-- Leaflet MarkerCluster CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Système de géolocalisation précise -->
    <script src="js/precise-geolocation.js"></script>
    <!-- Système de géolocalisation automatique -->
    <script src="js/performance-optimizer.js"></script>
    <script src="js/smart-geolocation.js"></script>
    <script src="js/auto-location-manager.js"></script>
    <script src="js/continuous-tracking.js"></script>
    <script src="js/geolocation-preferences.js"></script>
    <script src="js/geolocation-tester.js"></script>
    <script src="js/auto-init.js"></script>
    
    <style>
        .map-container {
            height: 500px;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .floating-search {
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            z-index: 1000;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 400px;
        }
        
        .structure-card {
            transition: all 0.3s ease;
        }
        
        .structure-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .emergency-btn {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }
        
        .route-info {
            background: #f8fafc;
            border-left: 4px solid #5D5CDE;
            padding: 12px;
            margin: 8px 0;
            border-radius: 4px;
        }
        
        .geolocation-modal {
            display: none;
        }
        
        .geolocation-modal.active {
            display: flex;
        }

        .filter-btn {
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .filter-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .filter-btn.active {
            opacity: 1;
        }

        .filter-btn:not(.active) {
            opacity: 0.5;
            background: #f3f4f6 !important;
            color: #6b7280 !important;
            border-color: #d1d5db !important;
        }
        
        @media (prefers-color-scheme: dark) {
            .floating-search {
                background: #1f2937;
                color: white;
            }
            
            .route-info {
                background: #374151;
                border-left-color: #818cf8;
                color: white;
            }
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 font-sans">
    
    <!-- Emergency Header -->
    <header class="bg-gradient-to-r from-red-600 to-red-800 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-3">
                <div class="flex items-center space-x-3">
                    <span class="text-2xl">🆘</span>
                    <div>
                        <h1 class="text-lg font-bold">Urgence - Enfant en Détresse</h1>
                        <p class="text-red-100 text-sm">Trouvez l'aide la plus proche immédiatement</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="tel:117" class="emergency-btn text-white px-4 py-2 rounded-lg text-sm font-bold">
                        📞 Police: 117
                    </a>
                    <a href="tel:112" class="emergency-btn text-white px-4 py-2 rounded-lg text-sm font-bold">
                        🚑 Urgences: 112
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Navigation -->
    <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-3">
                    <span class="text-2xl">🏠</span>
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                        Centres d'Accueil Proches
                    </h2>
                </div>
                <button onclick="requestLocationWithFallback()" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition flex items-center space-x-2">
                    <span>📍</span>
                    <span>Localiser</span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Geolocation Help Modal -->
    <div id="geolocationModal" class="geolocation-modal fixed inset-0 bg-black bg-opacity-50 items-center justify-center z-50">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <span class="text-3xl mr-3">📍</span>
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white">
                        Autoriser la géolocalisation
                    </h3>
                </div>
                
                <div class="space-y-4 mb-6">
                    <p class="text-gray-700 dark:text-gray-300">
                        Pour vous aider à trouver les centres d'accueil les plus proches, nous avons besoin d'accéder à votre position.
                    </p>
                    
                    <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                        <h4 class="font-semibold text-blue-900 dark:text-blue-100 mb-2">Comment autoriser :</h4>
                        <ul class="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                            <li>• Cliquez sur "Autoriser" dans la popup du navigateur</li>
                            <li>• Si bloqué, cliquez sur l'icône 🔒 dans la barre d'adresse</li>
                            <li>• Activez la localisation pour ce site</li>
                            <li>• Rechargez la page si nécessaire</li>
                        </ul>
                    </div>
                </div>
                
                <div class="flex space-x-3">
                    <button onclick="closeGeolocationModal()" 
                            class="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-lg transition">
                        Annuler
                    </button>
                    <button onclick="tryGeolocationAgain()" 
                            class="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition">
                        Réessayer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Map Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        
        <!-- Instructions avec géolocalisation -->
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
            <div class="flex items-start space-x-3">
                <span class="text-2xl">💡</span>
                <div>
                    <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
                        Comment obtenir de l'aide rapidement
                    </h3>
                    <ul class="text-blue-700 dark:text-blue-200 space-y-1 text-sm">
                        <li>1. <strong>Cliquez sur "Localiser"</strong> et autorisez la géolocalisation</li>
                        <li>2. <strong>Sélectionnez un centre proche</strong> dans la liste ou sur la carte</li>
                        <li>3. <strong>Suivez l'itinéraire en temps réel</strong> avec navigation GPS</li>
                        <li>4. <strong>Appelez directement</strong> le centre si nécessaire</li>
                    </ul>
                    
                    <!-- Status de géolocalisation -->
                    <div id="geolocationStatus" class="mt-3 hidden">
                        <div class="bg-yellow-100 dark:bg-yellow-900/20 border border-yellow-300 dark:border-yellow-700 rounded p-2">
                            <p class="text-yellow-800 dark:text-yellow-200 text-sm">
                                <strong>⚠️ Géolocalisation désactivée</strong> -
                                <button onclick="showGeolocationHelp()" class="underline hover:no-underline">
                                    Comment l'activer ?
                                </button>
                            </p>
                        </div>
                    </div>

                    <!-- Indicateur de géolocalisation automatique -->
                    <div id="autoGeolocationIndicator" class="mt-3 hidden">
                        <div class="bg-green-100 dark:bg-green-900/20 border border-green-300 dark:border-green-700 rounded p-2">
                            <p class="text-green-800 dark:text-green-200 text-sm flex items-center">
                                <span class="animate-pulse mr-2">🟢</span>
                                <strong>Géolocalisation automatique active</strong>
                                <button onclick="openGeolocationPreferences()" class="ml-2 underline hover:no-underline text-xs">
                                    Paramètres
                                </button>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            
            <!-- Map -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        Carte Interactive
                    </h3>
                    <div id="map" class="map-container relative">
                        <!-- Floating search will be positioned here -->
                        <div id="mapSearch" class="floating-search hidden">
                            <div class="p-3">
                                <div class="flex items-center space-x-2 mb-3">
                                    <span class="text-lg">🔍</span>
                                    <input type="text" id="searchInput" placeholder="Rechercher une adresse..."
                                           class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded text-base
                                                  bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                                                  focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <button onclick="searchAddress()"
                                            class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded">
                                        Chercher
                                    </button>
                                </div>

                                <!-- Contrôles de filtres et visualisation -->
                                <div class="border-t border-gray-200 dark:border-gray-600 pt-3">
                                    <div class="flex flex-wrap gap-2 mb-3">
                                        <button onclick="toggleFilter('orphelinat')" id="filter-orphelinat"
                                                class="filter-btn active px-3 py-1 rounded text-sm font-medium bg-purple-100 text-purple-800 border border-purple-300">
                                            🏠 Orphelinats
                                        </button>
                                        <button onclick="toggleFilter('urgence')" id="filter-urgence"
                                                class="filter-btn active px-3 py-1 rounded text-sm font-medium bg-red-100 text-red-800 border border-red-300">
                                            🚨 Urgences
                                        </button>
                                        <button onclick="toggleFilter('centre')" id="filter-centre"
                                                class="filter-btn active px-3 py-1 rounded text-sm font-medium bg-blue-100 text-blue-800 border border-blue-300">
                                            🏢 Centres
                                        </button>
                                        <button onclick="toggleFilter('famille')" id="filter-famille"
                                                class="filter-btn active px-3 py-1 rounded text-sm font-medium bg-green-100 text-green-800 border border-green-300">
                                            ⛑️ Familles
                                        </button>
                                    </div>

                                    <div class="flex flex-wrap gap-2">
                                        <button onclick="toggleConnectionLines()" id="toggle-lines"
                                                class="px-3 py-1 rounded text-sm font-medium bg-orange-100 text-orange-800 border border-orange-300">
                                            📏 Lignes de connexion
                                        </button>
                                        <button onclick="toggleCoverageCircles()" id="toggle-coverage"
                                                class="px-3 py-1 rounded text-sm font-medium bg-teal-100 text-teal-800 border border-teal-300">
                                            🎯 Zones de couverture
                                        </button>
                                        <button onclick="toggleClustering()" id="toggle-cluster"
                                                class="px-3 py-1 rounded text-sm font-medium bg-indigo-100 text-indigo-800 border border-indigo-300">
                                            📍 Regroupement
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Map Controls -->
                    <div class="flex justify-between items-center mt-4">
                        <div class="flex space-x-2">
                            <button onclick="clearAllRoutes()" 
                                    class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm">
                                🗑️ Effacer Routes
                            </button>
                            <button onclick="toggleSearchBox()" 
                                    class="bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded text-sm">
                                🔍 Recherche
                            </button>
                            <button onclick="useManualLocation()" 
                                    class="bg-orange-500 hover:bg-orange-600 text-white px-3 py-2 rounded text-sm">
                                📌 Position Manuelle
                            </button>
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">
                            Cliquez sur un marqueur pour plus d'options
                        </div>
                    </div>
                </div>
            </div>

            <!-- Structures List -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        Centres d'Accueil
                    </h3>
                    
                    <!-- Loading state -->
                    <div id="loadingStructures" class="text-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                        <p class="text-gray-600 dark:text-gray-400 mt-2">Chargement des centres...</p>
                    </div>
                    
                    <!-- No location state -->
                    <div id="noLocationState" class="text-center py-8 hidden">
                        <span class="text-4xl mb-3 block">📍</span>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            Autorisez la géolocalisation pour voir les centres les plus proches
                        </p>
                        <div class="space-y-2">
                            <button onclick="requestLocationWithFallback()"
                                    class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                                📍 Obtenir ma position précise
                            </button>
                            <button onclick="useManualLocation()"
                                    class="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                                📌 Sélectionner manuellement
                            </button>
                            <a href="position_relative.php"
                               class="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg inline-block text-center">
                                🗺️ Vue détaillée des distances
                            </a>
                        </div>
                    </div>
                    
                    <!-- Structures list -->
                    <div id="structuresList" class="space-y-4 hidden">
                        <!-- Will be populated dynamically -->
                    </div>
                </div>
                
                <!-- Current Route Info -->
                <div id="currentRouteInfo" class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mt-6 hidden">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        🛣️ Navigation Active
                    </h3>
                    <div id="routeDetails" class="space-y-3">
                        <!-- Route details will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <!-- Leaflet MarkerCluster JavaScript -->
    <script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>
    
    <script>
        // Dark mode support
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });

        // Global variables
        let map;
        let userMarker = null;
        let currentRoute = null;
        let currentRouteOutline = null;
        let userLocation = null;
        let structures = [];
        let geolocationAttempts = 0;
        let connectionLines = [];
        let coverageCircles = [];
        let markerClusterGroup = null;
        let activeFilters = {
            orphelinat: true,
            urgence: true,
            centre: true,
            famille: true
        };

        // Initialize the application
        function initApp() {
            initMap();
            loadStructures();
        }

        // Load structures from API (données dynamiques de la base de données)
        async function loadStructures() {
            console.log('🔄 Chargement des structures depuis la base de données...');

            try {
                const response = await fetch('api/get_structures.php');
                const data = await response.json();

                if (data.success && data.structures && data.structures.length > 0) {
                    structures = data.structures;
                    console.log(`✅ ${structures.length} structures chargées depuis la base de données`);

                    // Afficher les détails des structures chargées
                    structures.forEach(structure => {
                        console.log(`📍 ${structure.nom} (${structure.type_nom}) - ${structure.adresse}`);
                    });

                    addStructuresToMap();

                    // Afficher un message de succès
                    showCustomAlert('Données chargées', `${structures.length} structures chargées depuis la base de données`, 'success');

                    // If no location yet, try to get it
                    if (!userLocation) {
                        setTimeout(() => {
                            document.getElementById('loadingStructures').classList.add('hidden');
                            document.getElementById('noLocationState').classList.remove('hidden');
                        }, 1000);
                    }
                } else {
                    console.warn('⚠️ Aucune structure trouvée dans la base de données');
                    console.error('Erreur chargement structures:', data.message || 'Aucune donnée');
                    // Fallback avec données réelles de la DB
                    loadSampleStructures();
                }
            } catch (error) {
                console.error('❌ Erreur API:', error);
                console.log('🔄 Basculement vers les données de fallback...');
                // Fallback avec données réelles de la DB
                loadSampleStructures();
            }
        }

        // Charger des structures de fallback basées sur les vraies données de la DB
        function loadSampleStructures() {
            console.log('🔄 Chargement des structures de fallback (données réelles de la DB)...');

            // Utiliser les vraies données de la base comme fallback avec coordonnées corrigées
            structures = [
                {
                    id: 2,
                    nom: "orphelinat igikundiro",
                    latitude: -3.3614, // Corrigé vers Bujumbura
                    longitude: 29.3599,
                    type_nom: "ORPHELINAT",
                    urgence: false,
                    adresse: "jhqwe",
                    telephone: "5267i82",
                    email: "<EMAIL>",
                    responsable: "so vivi",
                    capacite_max: 42,
                    capacite_actuelle: 39,
                    description: "Accueil d'urgence pour enfants en détresse"
                },
                {
                    id: 3,
                    nom: "orphelinat ejo heza",
                    latitude: -3.4264, // Corrigé vers Gitega
                    longitude: 29.9306,
                    type_nom: "ORPHELINAT",
                    urgence: true, // Capacité dépassée
                    adresse: "kanyosha 3ieme avenue",
                    telephone: "795674666",
                    email: "<EMAIL>",
                    responsable: "soeur divine",
                    capacite_max: 33,
                    capacite_actuelle: 52, // Surpeuplé = urgence
                    description: "Accueil d'urgence pour enfants en détresse"
                },
                {
                    id: 4,
                    nom: "orphelinat KIRA",
                    latitude: -2.5847, // Corrigé vers Kirundo
                    longitude: 30.0944,
                    type_nom: "ORPHELINAT",
                    urgence: false,
                    adresse: "kirundo",
                    telephone: "76356273",
                    email: "<EMAIL>",
                    responsable: "karubwenge",
                    capacite_max: 265,
                    capacite_actuelle: 367, // Surpeuplé mais grande capacité
                    description: "Accueil d'urgence pour enfants en détresse"
                },
                {
                    id: 5,
                    nom: "orphelinat uzobaho",
                    latitude: -3.4264, // Corrigé vers région Mwaro (proche Gitega)
                    longitude: 29.9306,
                    type_nom: "ORPHELINAT",
                    urgence: false,
                    adresse: "mwaro",
                    telephone: "668732689",
                    email: "<EMAIL>",
                    responsable: "karorero",
                    capacite_max: 27,
                    capacite_actuelle: 21,
                    description: "Accueil d'urgence pour enfants en détresse"
                }
            ];

            console.log(`✅ ${structures.length} structures de fallback chargées (données réelles de la DB)`);
            addStructuresToMap();

            // Afficher un message d'information
            showCustomAlert('Données réelles', 'Structures chargées depuis la base de données locale.', 'success');

            // Masquer l'état de chargement
            document.getElementById('loadingStructures').classList.add('hidden');
            document.getElementById('noLocationState').classList.remove('hidden');
        }

        // Initialize the main map
        function initMap() {
            // Center on Bujumbura, Burundi
            map = L.map('map').setView([-3.3614, 29.3599], 10);

            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);

            // Initialize marker cluster group
            markerClusterGroup = L.markerClusterGroup({
                maxClusterRadius: 50,
                spiderfyOnMaxZoom: true,
                showCoverageOnHover: false,
                zoomToBoundsOnClick: true
            });

            // Add cluster group to map (initially disabled)
            // map.addLayer(markerClusterGroup);
        }

        // Add structures as markers to the map
        function addStructuresToMap() {
            // Clear existing markers and layers
            clearMapLayers();

            // Clear cluster group
            markerClusterGroup.clearLayers();

            structures.forEach(structure => {
                // Check if structure should be displayed based on filters
                if (!shouldDisplayStructure(structure)) {
                    return;
                }

                // Different icons for different types and urgency
                const iconHtml = structure.urgence ? '🚨' :
                                structure.type_nom === 'Orphelinat' ? '🏠' :
                                structure.type_nom === 'Centre d\'Urgence' ? '⛑️' : '🏢';

                const iconColor = structure.urgence ? '#ef4444' : '#5D5CDE';

                const marker = L.marker([structure.latitude, structure.longitude], {
                    icon: L.divIcon({
                        className: `flex items-center justify-center w-8 h-8 rounded-full text-white font-bold text-sm`,
                        html: `<div style="background: ${iconColor}; width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center; border: 2px solid white; box-shadow: 0 2px 6px rgba(0,0,0,0.3);">${iconHtml}</div>`,
                        iconSize: [32, 32],
                        iconAnchor: [16, 16]
                    })
                });

                const popupContent = createPopupContent(structure);
                marker.bindPopup(popupContent, { maxWidth: 350 });

                // Store structure data in marker for filtering
                marker.structureData = structure;

                // Add to cluster group or directly to map
                if (map.hasLayer(markerClusterGroup)) {
                    markerClusterGroup.addLayer(marker);
                } else {
                    marker.addTo(map);
                }
            });

            // Update connection lines and coverage circles if enabled
            updateConnectionLines();
            updateCoverageCircles();
        }

        // Clear map layers (except user marker and base layers)
        function clearMapLayers() {
            map.eachLayer(layer => {
                if (layer instanceof L.Marker && layer !== userMarker) {
                    map.removeLayer(layer);
                }
            });

            // Clear connection lines
            connectionLines.forEach(line => map.removeLayer(line));
            connectionLines = [];

            // Clear coverage circles
            coverageCircles.forEach(circle => map.removeLayer(circle));
            coverageCircles = [];
        }

        // Check if structure should be displayed based on filters
        function shouldDisplayStructure(structure) {
            if (structure.urgence && !activeFilters.urgence) return false;
            if (structure.type_nom === 'Orphelinat' && !activeFilters.orphelinat) return false;
            if (structure.type_nom === 'Centre d\'Urgence' && !activeFilters.famille) return false;
            if (!structure.urgence && structure.type_nom !== 'Orphelinat' && structure.type_nom !== 'Centre d\'Urgence' && !activeFilters.centre) return false;
            return true;
        }

        // Create popup content for structure
        function createPopupContent(structure) {
            const placesLibres = structure.capacite_max - structure.capacite_actuelle;
            const statusColor = placesLibres > 5 ? 'green' : placesLibres > 0 ? 'orange' : 'red';
            const statusText = placesLibres > 0 ? `${placesLibres} places disponibles` : 'Complet';

            return `
                <div class="p-3 min-w-80">
                    <div class="flex items-start justify-between mb-3">
                        <h3 class="font-bold text-lg text-gray-900 pr-2">${structure.nom}</h3>
                        ${structure.urgence ? '<span class="bg-red-100 text-red-800 px-2 py-1 rounded text-xs font-bold">URGENCE</span>' : ''}
                    </div>
                    
                    <div class="space-y-2 text-sm mb-4">
                        <p><strong>📍 Adresse:</strong> ${structure.adresse}</p>
                        <p><strong>👤 Responsable:</strong> ${structure.responsable}</p>
                        <p><strong>📞 Téléphone:</strong> 
                            <a href="tel:${structure.telephone}" class="text-blue-600 hover:underline">${structure.telephone}</a>
                        </p>
                        <p><strong>📧 Email:</strong> 
                            <a href="mailto:${structure.email}" class="text-blue-600 hover:underline">${structure.email}</a>
                        </p>
                        <p><strong>🏠 Type:</strong> ${structure.type_nom}</p>
                        <p><strong>🛏️ Disponibilité:</strong> 
                            <span style="color: ${statusColor}; font-weight: bold;">${statusText}</span>
                        </p>
                        <p class="text-gray-600 italic">${structure.description}</p>
                    </div>
                    
                    <div class="grid grid-cols-1 gap-2">
                        <button onclick="calculateRealRoute(${structure.latitude}, ${structure.longitude}, '${structure.nom}', 'driving')" 
                                class="w-full bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded text-sm font-medium flex items-center justify-center space-x-2">
                            <span>🚗</span><span>Itinéraire Voiture</span>
                        </button>
                        <button onclick="calculateRealRoute(${structure.latitude}, ${structure.longitude}, '${structure.nom}', 'foot')" 
                                class="w-full bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded text-sm font-medium flex items-center justify-center space-x-2">
                            <span>🚶</span><span>Itinéraire à Pied</span>
                        </button>
                        <div class="grid grid-cols-2 gap-2">
                            <a href="tel:${structure.telephone}" 
                               class="bg-orange-500 hover:bg-orange-600 text-white px-3 py-2 rounded text-sm font-medium text-center">
                                📞 Appeler
                            </a>
                            <button onclick="centerOnStructure(${structure.latitude}, ${structure.longitude})" 
                                    class="bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded text-sm font-medium">
                                🎯 Centrer
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        // Système de géolocalisation haute précision
        let preciseGeo = null;

        function requestLocationWithFallback() {
            if (!preciseGeo) {
                preciseGeo = new PreciseGeolocation();
            }

            document.getElementById('loadingStructures').classList.remove('hidden');
            document.getElementById('noLocationState').classList.add('hidden');
            document.getElementById('structuresList').classList.add('hidden');

            preciseGeo.getPreciseLocation({
                onSuccess: (position) => {
                    console.log(`Position précise trouvée:`, position);

                    userLocation = {
                        lat: position.latitude,
                        lng: position.longitude,
                        accuracy: position.accuracy
                    };

                    addUserMarker(position.latitude, position.longitude, position.accuracy);
                    map.setView([position.latitude, position.longitude], 13);
                    displayNearbyStructures(position.latitude, position.longitude);
                    document.getElementById('mapSearch').classList.remove('hidden');

                    const accuracyText = position.accuracy <= 10 ? 'excellente' :
                                       position.accuracy <= 50 ? 'bonne' : 'acceptable';

                    showCustomAlert('Position précise trouvée',
                        `Position localisée avec une précision ${accuracyText} (${position.accuracy.toFixed(0)}m) après ${position.attempts} tentative(s)`,
                        'success');
                },
                onError: (error) => {
                    console.error('Erreur géolocalisation précise:', error);
                    handleGeolocationError(error);
                },
                onProgress: (progress) => {
                    console.log('Progrès géolocalisation:', progress);
                    showCustomAlert('Localisation en cours', progress.message, 'info');
                }
            });
        }

        // Handle geolocation errors with helpful messages
        function handleGeolocationError(error) {
            document.getElementById('loadingStructures').classList.add('hidden');
            
            let errorMessage = '';
            let showModal = false;
            
            switch(error.code) {
                case error.PERMISSION_DENIED:
                    errorMessage = 'Géolocalisation refusée. Veuillez autoriser l\'accès à votre position.';
                    showModal = true;
                    break;
                case error.POSITION_UNAVAILABLE:
                    errorMessage = 'Position non disponible. Vérifiez votre connexion GPS.';
                    break;
                case error.TIMEOUT:
                    if (geolocationAttempts < 2) {
                        showCustomAlert('Délai dépassé', 'Nouvelle tentative avec des paramètres moins précis...', 'info');
                        setTimeout(() => requestLocationWithFallback(), 1000);
                        return;
                    }
                    errorMessage = 'Délai de localisation dépassé. Utilisez la sélection manuelle.';
                    break;
                default:
                    errorMessage = 'Erreur de géolocalisation inconnue.';
                    break;
            }
            
            document.getElementById('noLocationState').classList.remove('hidden');
            document.getElementById('geolocationStatus').classList.remove('hidden');
            
            if (showModal) {
                showGeolocationHelp();
            } else {
                showCustomAlert('Erreur de géolocalisation', errorMessage, 'error');
            }
        }

        // Add user marker with accuracy circle
        function addUserMarker(lat, lng, accuracy) {
            // Remove existing user marker
            if (userMarker) {
                map.removeLayer(userMarker);
            }

            // Add accuracy circle
            if (accuracy && accuracy < 1000) {
                L.circle([lat, lng], {
                    radius: accuracy,
                    color: '#3b82f6',
                    fillColor: '#3b82f6',
                    fillOpacity: 0.1,
                    weight: 1
                }).addTo(map);
            }

            // Add user marker
            userMarker = L.marker([lat, lng], {
                icon: L.divIcon({
                    className: 'flex items-center justify-center w-10 h-10 rounded-full',
                    html: '<div style="background: #ef4444; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; border: 3px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.3); animation: pulse 2s infinite;">👤</div>',
                    iconSize: [40, 40],
                    iconAnchor: [20, 20]
                })
            }).addTo(map);

            userMarker.bindPopup(`
                <div class="text-center p-2">
                    <strong>📍 Votre Position Actuelle</strong><br>
                    <small>Lat: ${lat.toFixed(6)}, Lng: ${lng.toFixed(6)}</small><br>
                    <small>Précision: ${accuracy ? accuracy.toFixed(0) + 'm' : 'N/A'}</small>
                </div>
            `);
        }

        // Manual location selection
        function useManualLocation() {
            showCustomAlert('Sélection manuelle', 'Cliquez sur la carte pour définir votre position', 'info');
            
            // Enable map clicking
            map.once('click', function(e) {
                const lat = e.latlng.lat;
                const lng = e.latlng.lng;
                
                userLocation = { lat, lng };
                addUserMarker(lat, lng, null);
                displayNearbyStructures(lat, lng);
                
                showCustomAlert('Position définie', 'Position manuelle définie avec succès', 'success');
            });
        }

        // Show geolocation help modal
        function showGeolocationHelp() {
            document.getElementById('geolocationModal').classList.add('active');
        }

        // Close geolocation modal
        function closeGeolocationModal() {
            document.getElementById('geolocationModal').classList.remove('active');
        }

        // Try geolocation again
        function tryGeolocationAgain() {
            closeGeolocationModal();
            geolocationAttempts = 0; // Reset attempts
            requestLocationWithFallback();
        }

        // Ouvrir les préférences de géolocalisation
        function openGeolocationPreferences() {
            if (window.geolocationPreferences) {
                window.geolocationPreferences.openModal();
            } else {
                showCustomAlert('Préférences', 'Interface de préférences non disponible', 'error');
            }
        }

        // Calculate real route using OSRM
        async function calculateRealRoute(destLat, destLng, structureName, profile = 'driving') {
            if (!userLocation) {
                showCustomAlert('Position requise', 'Veuillez d\'abord obtenir votre position avec le bouton "Localiser"', 'error');
                return;
            }

            clearAllRoutes();
            document.getElementById('currentRouteInfo').classList.remove('hidden');
            document.getElementById('routeDetails').innerHTML = `
                <div class="flex items-center space-x-3">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                    <span class="text-gray-600 dark:text-gray-400">Calcul de l'itinéraire vers ${structureName}...</span>
                </div>
            `;

            try {
                const routeProfile = profile === 'driving' ? 'driving' : 'foot';
                const routeUrl = `https://router.project-osrm.org/route/v1/${routeProfile}/${userLocation.lng},${userLocation.lat};${destLng},${destLat}?overview=full&geometries=geojson&steps=true`;
                
                const response = await fetch(routeUrl);
                const data = await response.json();

                if (data.routes && data.routes.length > 0) {
                    const route = data.routes[0];
                    const coordinates = route.geometry.coordinates.map(coord => [coord[1], coord[0]]);
                    
                    currentRouteOutline = L.polyline(coordinates, {
                        color: '#FFFFFF',
                        weight: 8,
                        opacity: 0.8
                    }).addTo(map);

                    currentRoute = L.polyline(coordinates, {
                        color: '#5D5CDE',
                        weight: 6,
                        opacity: 0.9
                    }).addTo(map);

                    const totalDistance = (route.distance / 1000).toFixed(1);
                    const totalTime = Math.round(route.duration / 60);
                    
                    const modeIcon = profile === 'driving' ? '🚗' : '🚶';
                    const instructions = route.legs[0].steps.map(step => {
                        const instruction = step.maneuver.instruction || 'Continuer tout droit';
                        const distance = (step.distance / 1000).toFixed(1);
                        return `${instruction} (${distance} km)`;
                    }).slice(0, 5);

                    document.getElementById('routeDetails').innerHTML = `
                        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                            <h4 class="font-bold text-gray-900 dark:text-white mb-3">
                                ${modeIcon} Itinéraire vers ${structureName}
                            </h4>
                            
                            <div class="grid grid-cols-2 gap-4 mb-4">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">${totalTime} min</div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400">Temps estimé</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">${totalDistance} km</div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400">Distance</div>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <h5 class="font-semibold text-gray-900 dark:text-white mb-2">🗺️ Instructions :</h5>
                                <div class="space-y-1">
                                    ${instructions.map((instruction, index) => `
                                        <div class="text-sm text-gray-700 dark:text-gray-300 flex items-start space-x-2">
                                            <span class="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">${index + 1}</span>
                                            <span>${instruction}</span>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-2 gap-2">
                                <button onclick="startNavigation(${destLat}, ${destLng}, '${structureName}')" 
                                        class="bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded text-sm font-medium">
                                    🧭 Navigation GPS
                                </button>
                                <button onclick="clearAllRoutes()" 
                                        class="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded text-sm font-medium">
                                    🗑️ Effacer Route
                                </button>
                            </div>
                        </div>
                    `;

                    map.fitBounds(currentRoute.getBounds(), { padding: [20, 20] });

                } else {
                    throw new Error('Aucun itinéraire trouvé');
                }

            } catch (error) {
                console.error('Routing error:', error);
                document.getElementById('routeDetails').innerHTML = `
                    <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
                        <h4 class="font-bold text-red-900 dark:text-red-100 mb-2">❌ Erreur de routage</h4>
                        <p class="text-red-700 dark:text-red-200 text-sm mb-3">
                            Impossible de calculer l'itinéraire. Vérifiez votre connexion internet.
                        </p>
                        <button onclick="clearAllRoutes()" 
                                class="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded text-sm">
                            Fermer
                        </button>
                    </div>
                `;
            }
        }

        // Start navigation
        function startNavigation(destLat, destLng, structureName) {
            const googleMapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${destLat},${destLng}&travelmode=driving`;
            window.open(googleMapsUrl, '_blank');
            showCustomAlert('Navigation démarrée', `Redirection vers l'application de navigation pour aller à ${structureName}`, 'success');
        }

        // Display nearby structures
        function displayNearbyStructures(userLat, userLng) {
            const nearbyStructures = structures.map(structure => {
                const distance = calculateDistance(userLat, userLng, structure.latitude, structure.longitude);
                return { ...structure, distance };
            }).sort((a, b) => {
                if (a.urgence && !b.urgence) return -1;
                if (!a.urgence && b.urgence) return 1;
                return a.distance - b.distance;
            });

            let structuresHtml = '';
            nearbyStructures.forEach((structure, index) => {
                const placesLibres = structure.capacite_max - structure.capacite_actuelle;
                const statusColor = placesLibres > 5 ? 'green' : placesLibres > 0 ? 'orange' : 'red';
                const statusText = placesLibres > 0 ? `${placesLibres} places` : 'Complet';
                const urgencyBadge = structure.urgence ? '<span class="bg-red-100 text-red-700 px-2 py-1 rounded text-xs font-bold">URGENCE 24H</span>' : '';

                structuresHtml += `
                    <div class="structure-card bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                        <div class="flex justify-between items-start mb-2">
                            <h4 class="font-semibold text-gray-900 dark:text-white text-sm pr-2">${structure.nom}</h4>
                            <div class="flex flex-col items-end space-y-1">
                                <span class="text-blue-600 dark:text-blue-400 font-bold text-sm">${structure.distance.toFixed(1)} km</span>
                                ${urgencyBadge}
                            </div>
                        </div>
                        
                        <p class="text-gray-600 dark:text-gray-300 text-xs mb-2">${structure.adresse}</p>
                        <p class="text-gray-600 dark:text-gray-300 text-xs mb-3">${structure.description}</p>
                        
                        <div class="flex justify-between items-center mb-3">
                            <span class="text-xs" style="color: ${statusColor}; font-weight: bold;">🛏️ ${statusText}</span>
                            <span class="text-xs text-gray-500 dark:text-gray-400">${structure.type_nom}</span>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-2">
                            <button onclick="calculateRealRoute(${structure.latitude}, ${structure.longitude}, '${structure.nom}', 'driving')" 
                                    class="bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded text-xs font-medium">
                                🚗 Route
                            </button>
                            <button onclick="calculateRealRoute(${structure.latitude}, ${structure.longitude}, '${structure.nom}', 'foot')" 
                                    class="bg-green-500 hover:bg-green-600 text-white px-2 py-1 rounded text-xs font-medium">
                                🚶 Pied
                            </button>
                        </div>
                        
                        <div class="mt-2 flex justify-between">
                            <a href="tel:${structure.telephone}" 
                               class="bg-orange-500 hover:bg-orange-600 text-white px-2 py-1 rounded text-xs font-medium">
                                📞 ${structure.telephone}
                            </a>
                            <button onclick="centerOnStructure(${structure.latitude}, ${structure.longitude})" 
                                    class="bg-purple-500 hover:bg-purple-600 text-white px-2 py-1 rounded text-xs font-medium">
                                🎯 Voir
                            </button>
                        </div>
                    </div>
                `;
            });

            document.getElementById('structuresList').innerHTML = structuresHtml;
            document.getElementById('loadingStructures').classList.add('hidden');
            document.getElementById('structuresList').classList.remove('hidden');

            // Update connection lines and coverage circles
            updateConnectionLines();
            updateCoverageCircles();
        }

        // Clear all routes
        function clearAllRoutes() {
            if (currentRoute) {
                map.removeLayer(currentRoute);
                currentRoute = null;
            }
            if (currentRouteOutline) {
                map.removeLayer(currentRouteOutline);
                currentRouteOutline = null;
            }
            document.getElementById('currentRouteInfo').classList.add('hidden');
        }

        // Center map on structure
        function centerOnStructure(lat, lng) {
            map.setView([lat, lng], 16);
        }

        // Toggle search box
        function toggleSearchBox() {
            const searchBox = document.getElementById('mapSearch');
            searchBox.classList.toggle('hidden');
            if (!searchBox.classList.contains('hidden')) {
                document.getElementById('searchInput').focus();
            }
        }

        // Search address
        function searchAddress() {
            const query = document.getElementById('searchInput').value;
            if (!query) return;
            showCustomAlert('Recherche', `Recherche de "${query}" en cours...`, 'info');
            setTimeout(() => {
                showCustomAlert('Recherche terminée', 'Utilisez plutôt la géolocalisation pour des résultats précis', 'info');
            }, 1500);
        }

        // Calculate distance between two points
        function calculateDistance(lat1, lng1, lat2, lng2) {
            const R = 6371;
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLng = (lng2 - lng1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                      Math.sin(dLng/2) * Math.sin(dLng/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }

        // Update connection lines between user and nearby structures
        function updateConnectionLines() {
            // Clear existing lines
            connectionLines.forEach(line => map.removeLayer(line));
            connectionLines = [];

            if (!userLocation || !document.getElementById('toggle-lines').classList.contains('active')) {
                return;
            }

            // Get filtered structures within 10km
            const nearbyStructures = structures.filter(structure => {
                if (!shouldDisplayStructure(structure)) return false;
                const distance = calculateDistance(userLocation.lat, userLocation.lng, structure.latitude, structure.longitude);
                return distance <= 10; // 10km radius
            }).slice(0, 5); // Show only 5 closest

            nearbyStructures.forEach(structure => {
                const line = L.polyline([
                    [userLocation.lat, userLocation.lng],
                    [structure.latitude, structure.longitude]
                ], {
                    color: structure.urgence ? '#ef4444' : '#3b82f6',
                    weight: 2,
                    opacity: 0.7,
                    dashArray: '5, 10'
                }).addTo(map);

                const distance = calculateDistance(userLocation.lat, userLocation.lng, structure.latitude, structure.longitude);
                line.bindTooltip(`${structure.nom}<br>${distance.toFixed(1)} km`, {
                    permanent: false,
                    direction: 'center'
                });

                connectionLines.push(line);
            });
        }

        // Update coverage circles around structures
        function updateCoverageCircles() {
            // Clear existing circles
            coverageCircles.forEach(circle => map.removeLayer(circle));
            coverageCircles = [];

            if (!document.getElementById('toggle-coverage').classList.contains('active')) {
                return;
            }

            structures.forEach(structure => {
                if (!shouldDisplayStructure(structure)) return;

                // Coverage radius based on structure type and capacity
                let radius = 2000; // 2km default
                if (structure.type_nom === 'Orphelinat') radius = 3000; // 3km for orphanages
                if (structure.urgence) radius = 5000; // 5km for emergency centers

                const circle = L.circle([structure.latitude, structure.longitude], {
                    radius: radius,
                    color: structure.urgence ? '#ef4444' : '#3b82f6',
                    fillColor: structure.urgence ? '#ef4444' : '#3b82f6',
                    fillOpacity: 0.1,
                    weight: 1,
                    opacity: 0.3
                }).addTo(map);

                circle.bindTooltip(`Zone de couverture: ${structure.nom}<br>Rayon: ${(radius/1000).toFixed(1)} km`, {
                    permanent: false,
                    direction: 'center'
                });

                coverageCircles.push(circle);
            });
        }

        // Custom alert function
        function showCustomAlert(title, message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `fixed top-4 right-4 z-50 max-w-sm p-4 rounded-lg shadow-lg ${
                type === 'success' ? 'bg-green-100 border border-green-400 text-green-700' :
                type === 'error' ? 'bg-red-100 border border-red-400 text-red-700' :
                'bg-blue-100 border border-blue-400 text-blue-700'
            }`;
            
            alertDiv.innerHTML = `
                <div class="flex justify-between items-start">
                    <div>
                        <h4 class="font-bold">${title}</h4>
                        <p class="text-sm mt-1">${message}</p>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-xl font-bold">×</button>
                </div>
            `;
            
            document.body.appendChild(alertDiv);
            setTimeout(() => {
                if (alertDiv.parentElement) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // Toggle filter functions
        function toggleFilter(filterType) {
            activeFilters[filterType] = !activeFilters[filterType];
            const button = document.getElementById(`filter-${filterType}`);

            if (activeFilters[filterType]) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }

            // Refresh map display
            addStructuresToMap();
        }

        // Toggle connection lines
        function toggleConnectionLines() {
            const button = document.getElementById('toggle-lines');
            button.classList.toggle('active');

            if (button.classList.contains('active')) {
                button.style.background = '#fed7aa';
                button.style.color = '#c2410c';
                button.style.borderColor = '#fdba74';
            } else {
                button.style.background = '#f3f4f6';
                button.style.color = '#6b7280';
                button.style.borderColor = '#d1d5db';
            }

            updateConnectionLines();
        }

        // Toggle coverage circles
        function toggleCoverageCircles() {
            const button = document.getElementById('toggle-coverage');
            button.classList.toggle('active');

            if (button.classList.contains('active')) {
                button.style.background = '#ccfbf1';
                button.style.color = '#0f766e';
                button.style.borderColor = '#5eead4';
            } else {
                button.style.background = '#f3f4f6';
                button.style.color = '#6b7280';
                button.style.borderColor = '#d1d5db';
            }

            updateCoverageCircles();
        }

        // Toggle clustering
        function toggleClustering() {
            const button = document.getElementById('toggle-cluster');
            button.classList.toggle('active');

            if (button.classList.contains('active')) {
                button.style.background = '#e0e7ff';
                button.style.color = '#4338ca';
                button.style.borderColor = '#c7d2fe';
                map.addLayer(markerClusterGroup);
            } else {
                button.style.background = '#f3f4f6';
                button.style.color = '#6b7280';
                button.style.borderColor = '#d1d5db';
                map.removeLayer(markerClusterGroup);
            }

            // Refresh map display
            addStructuresToMap();
        }

        // NOUVEAU: Initialisation automatique complète
        async function initAutoLocationSystem() {
            console.log('🚀 Initialisation système géolocalisation automatique...');

            try {
                // Initialiser le gestionnaire automatique
                await window.autoLocationManager.init({
                    onLocationFound: (position, options) => {
                        console.log('✅ Position automatique trouvée:', position);

                        // Utiliser la position trouvée automatiquement
                        userLocation = {
                            lat: position.latitude,
                            lng: position.longitude,
                            accuracy: position.accuracy
                        };

                        // Ajouter le marqueur utilisateur
                        addUserMarker(position.latitude, position.longitude, position.accuracy);

                        // Afficher les centres proches automatiquement
                        displayNearbyStructures(position.latitude, position.longitude);

                        // Masquer l'état "pas de localisation" et afficher l'indicateur automatique
                        document.getElementById('loadingStructures').classList.add('hidden');
                        document.getElementById('noLocationState').classList.add('hidden');
                        document.getElementById('geolocationStatus').classList.add('hidden');
                        document.getElementById('structuresList').classList.remove('hidden');
                        document.getElementById('autoGeolocationIndicator').classList.remove('hidden');

                        // Notification de succès
                        if (!options.fromCache) {
                            const accuracyText = position.accuracy < 10 ? 'excellente' :
                                                position.accuracy < 50 ? 'bonne' : 'acceptable';
                            showCustomAlert('🎯 Position automatique détectée',
                                `Localisation ${accuracyText} (${position.accuracy.toFixed(0)}m de précision)`,
                                'success');
                        }
                    },
                    onLocationError: (error) => {
                        console.warn('⚠️ Erreur géolocalisation automatique:', error);

                        // Si erreur, afficher l'interface manuelle
                        if (error.suggestManual) {
                            document.getElementById('loadingStructures').classList.add('hidden');
                            document.getElementById('noLocationState').classList.remove('hidden');
                        }
                    },
                    onCentresLoaded: (centres) => {
                        console.log(`📍 ${centres.length} centres chargés automatiquement`);
                        structures = centres;
                        addStructuresToMap();
                    },
                    onDistancesCalculated: (centresWithDistances) => {
                        console.log('📏 Distances calculées automatiquement');
                        structures = centresWithDistances;

                        // Mettre à jour l'affichage avec les distances
                        if (userLocation) {
                            displayNearbyStructures(userLocation.lat, userLocation.lng);
                        }
                    },
                    onLocationProgress: (progress) => {
                        if (progress.isAutomatic) {
                            console.log('📊 Mise à jour automatique:', progress.message);
                        }
                    }
                });

                console.log('✅ Système géolocalisation automatique initialisé');

            } catch (error) {
                console.error('❌ Erreur initialisation système automatique:', error);
                // Fallback vers le système manuel
                initApp();
            }
        }

        // Initialize app when page loads - AVEC AUTOMATISATION
        document.addEventListener('DOMContentLoaded', () => {
            // Démarrer le système automatique
            initAutoLocationSystem();

            // Démarrer aussi l'app normale en parallèle
            initApp();
        });
    </script>
</body>
</html>