<?php
// Connexion à la base de données avec PDO
try {
    $pdo = new PDO("mysql:host=localhost;dbname=gestion_enfant", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Récupération des centres d'accueil
    $stmt = $pdo->query("SELECT * FROM structure WHERE type_structure_id = 4");
    $centres = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    echo "Erreur de connexion : " . $e->getMessage();
}
?>

<?php include 'header_centre.php'; ?>

<div class="container">
    <h2>Liste des Centres d'Accueil</h2>
    <button class="btn-submit" onclick="window.location.href='enregistrement_centres.php'">Ajouter un Centre</button>
    <table>
        <thead>
            <tr>
                <th>Nom</th>
                <th>Adresse</th>
                <th>Téléphone</th> 
                <th>Email</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($centres as $centre): ?>
                <tr>
                    <td><?php echo htmlspecialchars($centre['nom']); ?></td>
                    <td><?php echo htmlspecialchars($centre['adresse']); ?></td>
                    <td><?php echo htmlspecialchars($centre['telephone']); ?></td>
                    <td><?php echo htmlspecialchars($centre['email']); ?></td>
                    <td>
                        <button class="btn-action" onclick="window.location.href='modifier_centre.php?id=<?php echo $centre['id']; ?>'">Modifier</button>
                        <button class="btn-action" onclick="if(confirm('Êtes-vous sûr de vouloir supprimer ce centre ?')) { window.location.href='supprimer_centre.php?id=<?php echo $centre['id']; ?>'; }">Supprimer</button>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>