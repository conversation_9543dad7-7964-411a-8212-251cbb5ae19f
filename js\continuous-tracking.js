/**
 * Système de Surveillance Continue Intelligente - Umwana Voice
 * Tracking en temps réel avec optimisation batterie et performance
 */

class ContinuousTracking {
    constructor() {
        this.isActive = false;
        this.watchId = null;
        this.lastPosition = null;
        this.trackingHistory = [];
        this.batteryLevel = 1.0;
        this.isCharging = false;
        this.adaptiveSettings = {
            interval: 60000,        // 1 minute par défaut
            accuracy: true,         // Haute précision par défaut
            timeout: 15000,         // 15 secondes timeout
            maxAge: 30000          // Cache 30 secondes
        };
        
        // Statistiques
        this.stats = {
            totalUpdates: 0,
            significantMoves: 0,
            batteryOptimizations: 0,
            startTime: Date.now()
        };
        
        console.log('🔄 ContinuousTracking initialisé');
        this.init();
    }

    /**
     * Initialisation du système
     */
    async init() {
        // Initialiser la surveillance batterie
        await this.initBatteryMonitoring();
        
        // Configurer les événements de visibilité
        this.setupVisibilityHandling();
        
        // Configurer la détection de mouvement
        this.setupMotionDetection();
        
        console.log('✅ ContinuousTracking prêt');
    }

    /**
     * Démarrer la surveillance continue
     */
    start() {
        if (this.isActive) {
            console.log('⚠️ Surveillance continue déjà active');
            return;
        }

        console.log('🚀 Démarrage surveillance continue intelligente...');
        this.isActive = true;
        this.stats.startTime = Date.now();
        
        this.startTracking();
        this.showTrackingIndicator();
    }

    /**
     * Arrêter la surveillance continue
     */
    stop() {
        if (!this.isActive) return;

        console.log('⏹️ Arrêt surveillance continue');
        this.isActive = false;
        
        if (this.watchId) {
            navigator.geolocation.clearWatch(this.watchId);
            this.watchId = null;
        }
        
        this.hideTrackingIndicator();
        this.logStats();
    }

    /**
     * Démarrer le tracking GPS
     */
    startTracking() {
        const options = {
            enableHighAccuracy: this.adaptiveSettings.accuracy,
            timeout: this.adaptiveSettings.timeout,
            maximumAge: this.adaptiveSettings.maxAge
        };

        console.log('📡 Configuration tracking:', options);

        this.watchId = navigator.geolocation.watchPosition(
            (position) => this.handlePositionUpdate(position),
            (error) => this.handleTrackingError(error),
            options
        );
    }

    /**
     * Gérer les mises à jour de position
     */
    handlePositionUpdate(position) {
        this.stats.totalUpdates++;
        
        const newPosition = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: position.timestamp,
            speed: position.coords.speed,
            heading: position.coords.heading
        };

        // Vérifier si le mouvement est significatif
        if (this.isSignificantMove(newPosition)) {
            this.stats.significantMoves++;
            
            console.log('📍 Mouvement significatif détecté:', newPosition);
            
            // Sauvegarder dans l'historique
            this.trackingHistory.push(newPosition);
            
            // Limiter l'historique à 50 positions
            if (this.trackingHistory.length > 50) {
                this.trackingHistory.shift();
            }
            
            // Notifier les callbacks
            this.notifyPositionUpdate(newPosition);
            
            // Mettre à jour l'interface
            this.updateTrackingInterface(newPosition);
        }

        this.lastPosition = newPosition;
        
        // Optimiser selon la batterie
        this.optimizeForBattery();
    }

    /**
     * Vérifier si le mouvement est significatif
     */
    isSignificantMove(newPosition) {
        if (!this.lastPosition) return true;

        const distance = this.calculateDistance(
            this.lastPosition.latitude,
            this.lastPosition.longitude,
            newPosition.latitude,
            newPosition.longitude
        );

        // Seuil adaptatif selon la précision
        const threshold = newPosition.accuracy > 50 ? 0.05 : 0.01; // 50m ou 10m
        
        return distance > threshold;
    }

    /**
     * Calculer la distance entre deux points
     */
    calculateDistance(lat1, lng1, lat2, lng2) {
        const R = 6371; // Rayon de la Terre en km
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLng = (lng2 - lng1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                  Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }

    /**
     * Optimiser selon le niveau de batterie
     */
    optimizeForBattery() {
        let needsRestart = false;
        const oldSettings = { ...this.adaptiveSettings };

        if (this.batteryLevel < 0.15) { // < 15%
            // Mode économie extrême
            this.adaptiveSettings.interval = 300000; // 5 minutes
            this.adaptiveSettings.accuracy = false;
            this.adaptiveSettings.timeout = 30000;
            this.adaptiveSettings.maxAge = 120000; // 2 minutes
            needsRestart = true;
            
        } else if (this.batteryLevel < 0.30) { // < 30%
            // Mode économie
            this.adaptiveSettings.interval = 180000; // 3 minutes
            this.adaptiveSettings.accuracy = false;
            this.adaptiveSettings.timeout = 20000;
            this.adaptiveSettings.maxAge = 90000; // 1.5 minutes
            needsRestart = true;
            
        } else if (this.batteryLevel < 0.50) { // < 50%
            // Mode équilibré
            this.adaptiveSettings.interval = 120000; // 2 minutes
            this.adaptiveSettings.accuracy = true;
            this.adaptiveSettings.timeout = 15000;
            this.adaptiveSettings.maxAge = 60000; // 1 minute
            needsRestart = true;
            
        } else if (this.isCharging || this.batteryLevel > 0.80) { // > 80% ou en charge
            // Mode performance
            this.adaptiveSettings.interval = 60000; // 1 minute
            this.adaptiveSettings.accuracy = true;
            this.adaptiveSettings.timeout = 10000;
            this.adaptiveSettings.maxAge = 30000; // 30 secondes
            needsRestart = true;
        }

        // Redémarrer le tracking si les paramètres ont changé
        if (needsRestart && JSON.stringify(oldSettings) !== JSON.stringify(this.adaptiveSettings)) {
            console.log('🔋 Optimisation batterie - redémarrage tracking:', this.adaptiveSettings);
            this.stats.batteryOptimizations++;
            
            if (this.watchId) {
                navigator.geolocation.clearWatch(this.watchId);
                setTimeout(() => this.startTracking(), 1000);
            }
        }
    }

    /**
     * Initialiser la surveillance de batterie
     */
    async initBatteryMonitoring() {
        try {
            if ('getBattery' in navigator) {
                const battery = await navigator.getBattery();
                
                this.batteryLevel = battery.level;
                this.isCharging = battery.charging;
                
                console.log(`🔋 Batterie: ${(battery.level * 100).toFixed(0)}% ${battery.charging ? '(en charge)' : ''}`);
                
                // Écouter les changements
                battery.addEventListener('levelchange', () => {
                    this.batteryLevel = battery.level;
                    console.log(`🔋 Niveau batterie: ${(battery.level * 100).toFixed(0)}%`);
                });
                
                battery.addEventListener('chargingchange', () => {
                    this.isCharging = battery.charging;
                    console.log(`🔌 État charge: ${battery.charging ? 'En charge' : 'Sur batterie'}`);
                });
            }
        } catch (error) {
            console.warn('⚠️ API Batterie non supportée:', error);
        }
    }

    /**
     * Configurer la gestion de visibilité
     */
    setupVisibilityHandling() {
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                console.log('📱 Page cachée - réduction fréquence tracking');
                // Réduire la fréquence quand la page n'est pas visible
                if (this.isActive) {
                    this.adaptiveSettings.interval *= 2;
                    this.optimizeForBattery();
                }
            } else {
                console.log('📱 Page visible - restauration fréquence tracking');
                // Restaurer la fréquence normale
                if (this.isActive) {
                    this.adaptiveSettings.interval = 60000; // Retour à 1 minute
                    this.optimizeForBattery();
                }
            }
        });
    }

    /**
     * Configurer la détection de mouvement
     */
    setupMotionDetection() {
        if ('DeviceMotionEvent' in window) {
            let motionThreshold = 0;
            
            window.addEventListener('devicemotion', (event) => {
                const acceleration = event.accelerationIncludingGravity;
                if (acceleration) {
                    const totalAcceleration = Math.sqrt(
                        acceleration.x * acceleration.x +
                        acceleration.y * acceleration.y +
                        acceleration.z * acceleration.z
                    );
                    
                    // Si mouvement détecté, augmenter temporairement la précision
                    if (totalAcceleration > motionThreshold + 2) {
                        if (this.isActive && this.batteryLevel > 0.3) {
                            this.adaptiveSettings.accuracy = true;
                            this.adaptiveSettings.interval = Math.min(this.adaptiveSettings.interval, 60000);
                        }
                    }
                }
            });
        }
    }

    /**
     * Afficher l'indicateur de tracking
     */
    showTrackingIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'tracking-indicator';
        indicator.innerHTML = `
            <div class="fixed bottom-4 right-4 bg-green-600 text-white px-3 py-2 rounded-full shadow-lg flex items-center space-x-2 text-sm z-50">
                <div class="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                <span>Suivi actif</span>
                <button onclick="window.continuousTracking.stop()" class="text-green-200 hover:text-white ml-2">×</button>
            </div>
        `;
        
        document.body.appendChild(indicator);
    }

    /**
     * Masquer l'indicateur de tracking
     */
    hideTrackingIndicator() {
        const indicator = document.getElementById('tracking-indicator');
        if (indicator) {
            indicator.remove();
        }
    }

    /**
     * Mettre à jour l'interface de tracking
     */
    updateTrackingInterface(position) {
        const indicator = document.getElementById('tracking-indicator');
        if (indicator) {
            const accuracy = position.accuracy < 10 ? 'excellente' : 
                            position.accuracy < 50 ? 'bonne' : 'acceptable';
            
            indicator.title = `Position mise à jour - Précision ${accuracy} (${position.accuracy.toFixed(0)}m)`;
        }
    }

    /**
     * Notifier les callbacks de mise à jour
     */
    notifyPositionUpdate(position) {
        // Notifier AutoLocationManager si disponible
        if (window.autoLocationManager && window.autoLocationManager.handleLocationSuccess) {
            window.autoLocationManager.handleLocationSuccess(position, { 
                silent: true, 
                fromTracking: true 
            });
        }
        
        // Événement personnalisé
        window.dispatchEvent(new CustomEvent('continuousPositionUpdate', {
            detail: { position, stats: this.stats }
        }));
    }

    /**
     * Gérer les erreurs de tracking
     */
    handleTrackingError(error) {
        console.warn('⚠️ Erreur tracking continu:', error);
        
        // Retry intelligent selon le type d'erreur
        if (error.code === 3) { // TIMEOUT
            // Augmenter le timeout et réessayer
            this.adaptiveSettings.timeout += 5000;
            setTimeout(() => {
                if (this.isActive) {
                    this.startTracking();
                }
            }, 10000);
        }
    }

    /**
     * Afficher les statistiques
     */
    logStats() {
        const duration = (Date.now() - this.stats.startTime) / 1000 / 60; // minutes
        
        console.log('📊 Statistiques Tracking Continu:');
        console.log(`   Durée: ${duration.toFixed(1)} minutes`);
        console.log(`   Mises à jour totales: ${this.stats.totalUpdates}`);
        console.log(`   Mouvements significatifs: ${this.stats.significantMoves}`);
        console.log(`   Optimisations batterie: ${this.stats.batteryOptimizations}`);
        console.log(`   Positions en historique: ${this.trackingHistory.length}`);
    }

    /**
     * Obtenir l'historique des positions
     */
    getTrackingHistory() {
        return [...this.trackingHistory];
    }

    /**
     * Obtenir les statistiques
     */
    getStats() {
        return {
            ...this.stats,
            duration: (Date.now() - this.stats.startTime) / 1000 / 60,
            isActive: this.isActive,
            batteryLevel: this.batteryLevel,
            isCharging: this.isCharging,
            currentSettings: { ...this.adaptiveSettings }
        };
    }
}

// Styles CSS
const styles = `
<style>
#tracking-indicator {
    animation: slideInFromBottom 0.3s ease-out;
}

@keyframes slideInFromBottom {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}
</style>
`;

// Injecter les styles
document.head.insertAdjacentHTML('beforeend', styles);

// Instance globale
window.continuousTracking = new ContinuousTracking();

console.log('✅ ContinuousTracking prêt');
