<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Donn<PERSON> Dynami<PERSON> Voice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .test-section h2 {
            color: #495057;
            margin-bottom: 15px;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .structure-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .structure-card h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        
        .structure-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .info-item {
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .urgence {
            background: #f8d7da !important;
            color: #721c24;
            font-weight: bold;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .before, .after {
            padding: 15px;
            border-radius: 8px;
        }
        
        .before {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        
        .after {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Test des Données Dynamiques - Umwana Voice</h1>
        
        <div class="test-section">
            <h2>📊 Test de l'API get_structures.php</h2>
            <div id="apiStatus" class="status loading">⏳ Test en cours...</div>
            <button onclick="testAPI()">🔄 Relancer le test API</button>
            <div id="apiResults"></div>
        </div>
        
        <div class="test-section">
            <h2>📍 Structures Chargées</h2>
            <div id="structuresStatus" class="status loading">⏳ Chargement des structures...</div>
            <div id="structuresCount"></div>
            <div id="structuresList"></div>
        </div>
        
        <div class="test-section">
            <h2>🔄 Comparaison Avant/Après</h2>
            <div class="comparison">
                <div class="before">
                    <h3>❌ AVANT (Données Simulées)</h3>
                    <ul>
                        <li>Structures fictives (Orphelinat Espoir, Centre Kamenge...)</li>
                        <li>Coordonnées inventées</li>
                        <li>Informations génériques</li>
                        <li>Pas de connexion à la base de données</li>
                        <li>Données statiques codées en dur</li>
                    </ul>
                </div>
                <div class="after">
                    <h3>✅ APRÈS (Données Dynamiques)</h3>
                    <ul>
                        <li>Structures réelles de la base de données</li>
                        <li>Coordonnées GPS corrigées pour le Burundi</li>
                        <li>Informations complètes (responsable, téléphone, email)</li>
                        <li>Connexion directe à la base de données</li>
                        <li>Données dynamiques mises à jour en temps réel</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔗 Liens de Test</h2>
            <button onclick="window.open('Home1.php', '_blank')">🏠 Tester Home1.php</button>
            <button onclick="window.open('home.php', '_blank')">🏡 Tester home.php</button>
            <button onclick="window.open('api/get_structures.php', '_blank')">🔌 Voir API brute</button>
        </div>
    </div>

    <script>
        let structures = [];
        
        // Test de l'API au chargement de la page
        window.onload = function() {
            testAPI();
        };
        
        async function testAPI() {
            const statusDiv = document.getElementById('apiStatus');
            const resultsDiv = document.getElementById('apiResults');
            
            statusDiv.className = 'status loading';
            statusDiv.textContent = '⏳ Test de l\'API en cours...';
            resultsDiv.innerHTML = '';
            
            try {
                const response = await fetch('api/get_structures.php');
                const data = await response.json();
                
                if (data.success && data.structures) {
                    structures = data.structures;
                    
                    statusDiv.className = 'status success';
                    statusDiv.textContent = `✅ API fonctionnelle - ${data.structures.length} structures trouvées`;
                    
                    resultsDiv.innerHTML = `
                        <div style="margin-top: 15px;">
                            <strong>📊 Résultats de l'API:</strong>
                            <pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px;">
${JSON.stringify(data, null, 2)}
                            </pre>
                        </div>
                    `;
                    
                    displayStructures();
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = '❌ Erreur API: ' + (data.message || 'Aucune donnée');
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ Erreur de connexion: ' + error.message;
            }
        }
        
        function displayStructures() {
            const statusDiv = document.getElementById('structuresStatus');
            const countDiv = document.getElementById('structuresCount');
            const listDiv = document.getElementById('structuresList');
            
            if (structures.length > 0) {
                statusDiv.className = 'status success';
                statusDiv.textContent = `✅ ${structures.length} structures chargées depuis la base de données`;
                
                countDiv.innerHTML = `
                    <div style="margin: 15px 0;">
                        <strong>📈 Statistiques:</strong>
                        <ul>
                            <li>Total structures: ${structures.length}</li>
                            <li>Structures urgentes: ${structures.filter(s => s.urgence).length}</li>
                            <li>Capacité totale: ${structures.reduce((sum, s) => sum + s.capacite_max, 0)}</li>
                            <li>Occupation actuelle: ${structures.reduce((sum, s) => sum + s.capacite_actuelle, 0)}</li>
                        </ul>
                    </div>
                `;
                
                listDiv.innerHTML = structures.map(structure => `
                    <div class="structure-card">
                        <h3>${structure.nom} ${structure.urgence ? '⚠️ URGENT' : ''}</h3>
                        <div class="structure-info">
                            <div class="info-item">📍 <strong>Adresse:</strong> ${structure.adresse}</div>
                            <div class="info-item">🗺️ <strong>Coordonnées:</strong> ${structure.latitude}, ${structure.longitude}</div>
                            <div class="info-item">👥 <strong>Capacité:</strong> ${structure.capacite_actuelle}/${structure.capacite_max}</div>
                            <div class="info-item">📞 <strong>Téléphone:</strong> ${structure.telephone}</div>
                            <div class="info-item">👤 <strong>Responsable:</strong> ${structure.responsable}</div>
                            <div class="info-item">📧 <strong>Email:</strong> ${structure.email}</div>
                            <div class="info-item ${structure.urgence ? 'urgence' : ''}">
                                🏷️ <strong>Type:</strong> ${structure.type_nom} ${structure.urgence ? '(URGENT)' : ''}
                            </div>
                        </div>
                    </div>
                `).join('');
            } else {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ Aucune structure trouvée';
            }
        }
    </script>
</body>
</html>
