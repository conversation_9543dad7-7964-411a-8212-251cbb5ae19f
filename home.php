<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>UmwanaVoice - Accueil</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">

  <!-- Leaflet CSS pour la carte -->
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
  <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
  <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />

  <!-- Scripts de géolocalisation automatique -->
  <script src="js/performance-optimizer.js"></script>
  <script src="js/smart-geolocation.js"></script>
  <script src="js/auto-location-manager.js"></script>
  <script src="js/continuous-tracking.js"></script>
  <script src="js/geolocation-preferences.js"></script>
  <script src="js/geolocation-tester.js"></script>
  <script src="js/auto-init.js"></script>

  <style>
    :root {
      --bleu: #1e3d59;
      --bleu-clair: #2a4f70;
      --jaune: #f7b731;
      --jaune-foncé: #e0a800;
      --gris-fond: #f4f6fb;
      --carte-fond: #ffffff;
      --ombre: rgba(0, 0, 0, 0.08);
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Poppins', sans-serif;
      background-color: var(--gris-fond);
      color: #2c3e50;
    }

    a {
      text-decoration: none;
      color: inherit;
      transition: 0.3s ease;
    }

    a:hover {
      color: var(--jaune);
    }

    header {
      background: linear-gradient(to right, #1e3d59, #2a4f70);
      color: white;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 40px;
      box-shadow: 0 3px 10px var(--ombre);
    }

    .logo {
      display: flex;
      align-items: center;
    }

    .logo img {
      height: 45px;
      margin-right: 12px;
    }

    .logo h1 {
      font-size: 26px;
      font-weight: 700;
    }

    .btn-connexion {
      background-color: var(--jaune);
      border: none;
      padding: 10px 22px;
      border-radius: 8px;
      font-weight: 600;
      color: #fff;
      cursor: pointer;
      transition: background-color 0.3s;
    }

    .btn-connexion:hover {
      background-color: var(--jaune-foncé);
    }

    .hero {
      background: linear-gradient(to right, rgba(30, 61, 89, 0.85), rgba(30, 61, 89, 0.5)),
        url('https://images.unsplash.com/photo-1601758123927-196b3f29ba3a') center/cover no-repeat;
      color: white;
      padding: 100px 20px;
      text-align: center;
    }

    .hero h2 {
      font-size: 46px;
      margin-bottom: 20px;
    }

    .hero p {
      font-size: 18px;
      max-width: 700px;
      margin: 0 auto;
    }

    .titre-interface {
      text-align: center;
      margin: 60px 20px 20px;
      font-size: 30px;
      font-weight: 700;
      color: var(--bleu);
    }

    .interfaces {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 30px;
      padding: 30px 20px 60px;
    }

    .carte-interface {
      background-color: var(--carte-fond);
      width: 260px;
      padding: 30px 20px;
      border-radius: 16px;
      text-align: center;
      box-shadow: 0 10px 20px var(--ombre);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      border-top: 4px solid var(--bleu);
    }

    .carte-interface:hover {
      transform: translateY(-6px);
      box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
    }

    .carte-interface h3 {
      margin-bottom: 15px;
      color: var(--bleu);
      font-size: 20px;
      font-weight: 600;
    }

    .carte-interface a {
      display: inline-block;
      margin-top: 15px;
      padding: 10px 18px;
      background-color: var(--bleu);
      color: white;
      border-radius: 6px;
      font-size: 14px;
      transition: background-color 0.3s ease;
    }

    .carte-interface a:hover {
      background-color: var(--bleu-clair);
    }

    .localisation {
      background-color: #fff;
      padding: 60px 20px;
      text-align: center;
    }

    .localisation h2 {
      color: var(--bleu);
      margin-bottom: 30px;
      font-size: 26px;
    }

    .map {
      width: 100%;
      max-width: 900px;
      height: 500px;
      margin: 0 auto;
      border-radius: 12px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
      position: relative;
      overflow: hidden;
    }

    /* Styles pour les indicateurs de géolocalisation */
    .geolocation-indicator {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 1000;
      background: white;
      padding: 8px 12px;
      border-radius: 20px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .geolocation-indicator.active {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      color: #155724;
    }

    .geolocation-indicator.error {
      background: #f8d7da;
      border: 1px solid #f5c6cb;
      color: #721c24;
    }

    .geolocation-indicator.loading {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      color: #856404;
    }

    .pulse {
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    /* Styles pour les contrôles de carte */
    .map-controls {
      position: absolute;
      top: 10px;
      left: 10px;
      z-index: 1000;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .map-control-btn {
      background: white;
      border: none;
      padding: 8px 12px;
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      cursor: pointer;
      font-size: 12px;
      transition: all 0.2s ease;
    }

    .map-control-btn:hover {
      background: #f8f9fa;
      transform: translateY(-1px);
    }

    .map-control-btn.active {
      background: var(--jaune);
      color: white;
    }

    footer {
      background-color: var(--bleu);
      color: white;
      text-align: center;
      padding: 30px 20px;
      margin-top: 50px;
    }

    footer p {
      margin: 8px 0;
      font-size: 14px;
    }

    @media screen and (max-width: 768px) {
      .hero h2 {
        font-size: 32px;
      }

      .carte-interface {
        width: 90%;
      }

      .interfaces {
        padding: 20px 10px 40px;
      }
    }
  </style>
</head>

<body>

<header>
  <div class="logo">
    <img src="https://cdn-icons-png.flaticon.com/512/732/732212.png" alt="Logo">
    <h1>UmwanaVoice</h1>
  </div>
  <button class="btn-connexion" onclick="window.location='connexion.php'">Se Connecter</button>
</header>

<section class="hero">
  <h2>Bienvenue sur UmwanaVoice</h2>
  <p>Une plateforme nationale dédiée à la gestion, au suivi et à la protection des enfants vulnérables au Burundi.</p>
</section>

<section class="titre-interface">
  <h2>Choisissez votre interface de travail</h2>
</section>

<section class="interfaces">
  <div class="carte-interface">
    <h3>Admin</h3>
    <a href="login.php?role=admin">Accéder à l'interface</a>
  </div>
  <div class="carte-interface">
    <h3>Directeur des Centres</h3>
    <a href="login.php?role=directeur">Accéder à l'interface</a>
  </div>
  <div class="carte-interface">
    <h3>ONG</h3>
    <a href="login.php?role=ong">Accéder à l'interface</a>
  </div>
  <div class="carte-interface">
    <h3>Suivi des Enfants</h3>
    <a href="login.php?role=suivi">Accéder à l'interface</a>
  </div>
  <div class="carte-interface">
    <h3>Parrainage</h3>
    <a href="condition_parrainage.php">Accéder à l'interface</a>
  </div>
</section>

<section class="localisation">
  <h2>Traçabilité & Géolocalisation des Centres</h2>
  <p style="margin-bottom: 20px; color: #666;">Découvrez les centres d'aide près de chez vous grâce à notre système de géolocalisation automatique</p>

  <div class="map" id="mapContainer">
    <!-- Indicateur de géolocalisation -->
    <div id="geolocationIndicator" class="geolocation-indicator loading">
      <span class="pulse">📍</span>
      <span id="indicatorText">Localisation en cours...</span>
    </div>

    <!-- Contrôles de carte -->
    <div class="map-controls">
      <button id="locateBtn" class="map-control-btn" onclick="requestUserLocation()" title="Me localiser">
        📍 Ma position
      </button>
      <button id="preferencesBtn" class="map-control-btn" onclick="openGeolocationPreferences()" title="Paramètres">
        ⚙️ Paramètres
      </button>
      <button id="testBtn" class="map-control-btn" onclick="runGeolocationTests()" title="Tests" style="display: none;">
        🧪 Tests
      </button>
    </div>

    <!-- Carte Leaflet -->
    <div id="map" style="height: 100%; width: 100%;"></div>
  </div>

  <!-- Informations sur la géolocalisation -->
  <div style="margin-top: 20px; text-align: left; max-width: 900px; margin-left: auto; margin-right: auto;">
    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid var(--jaune);">
      <h3 style="color: var(--bleu); margin-bottom: 10px;">🎯 Géolocalisation Automatique</h3>
      <ul style="color: #666; line-height: 1.6;">
        <li>✅ <strong>Détection automatique</strong> de votre position au chargement</li>
        <li>✅ <strong>Centres les plus proches</strong> affichés automatiquement</li>
        <li>✅ <strong>Optimisation batterie</strong> pour un suivi intelligent</li>
        <li>✅ <strong>Paramètres personnalisables</strong> selon vos préférences</li>
      </ul>
    </div>
  </div>
</section>

<footer>
  <p><strong>UmwanaVoice</strong> - Plateforme du Ministère de la Solidarité Nationale</p>
  <p>Contact : <EMAIL> | +257 22 00 00 00</p>
  <p>&copy; 2025 Tous droits réservés</p>
</footer>

<!-- Leaflet JavaScript -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>

<script>
// Variables globales
let map = null;
let userMarker = null;
let userLocation = null;
let structures = [];
let markerClusterGroup = null;

// Initialisation de la carte
function initMap() {
    // Créer la carte centrée sur le Burundi
    map = L.map('map').setView([-3.3731, 29.9189], 8);

    // Ajouter les tuiles OpenStreetMap
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    // Initialiser le cluster de marqueurs
    markerClusterGroup = L.markerClusterGroup({
        chunkedLoading: true,
        maxClusterRadius: 50
    });
    map.addLayer(markerClusterGroup);

    console.log('✅ Carte initialisée');
}

// Demander la localisation utilisateur
function requestUserLocation() {
    updateIndicator('loading', 'Localisation en cours...');

    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            (position) => {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                const accuracy = position.coords.accuracy;

                userLocation = { lat, lng, accuracy };

                // Ajouter le marqueur utilisateur
                addUserMarker(lat, lng, accuracy);

                // Centrer la carte
                map.setView([lat, lng], 12);

                // Charger les structures proches
                loadNearbyStructures(lat, lng);

                updateIndicator('active', `Position trouvée (${accuracy.toFixed(0)}m)`);
            },
            (error) => {
                console.error('Erreur géolocalisation:', error);
                let message = 'Erreur de localisation';

                switch(error.code) {
                    case error.PERMISSION_DENIED:
                        message = 'Géolocalisation refusée';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        message = 'Position indisponible';
                        break;
                    case error.TIMEOUT:
                        message = 'Délai dépassé';
                        break;
                }

                updateIndicator('error', message);
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 300000
            }
        );
    } else {
        updateIndicator('error', 'Géolocalisation non supportée');
    }
}

// Ajouter le marqueur utilisateur
function addUserMarker(lat, lng, accuracy) {
    // Supprimer l'ancien marqueur
    if (userMarker) {
        map.removeLayer(userMarker);
    }

    // Créer le nouveau marqueur
    userMarker = L.marker([lat, lng], {
        icon: L.divIcon({
            className: 'user-marker',
            html: '<div style="background: #007bff; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 6px rgba(0,0,0,0.3);"></div>',
            iconSize: [20, 20],
            iconAnchor: [10, 10]
        })
    }).addTo(map);

    // Ajouter le cercle de précision
    L.circle([lat, lng], {
        radius: accuracy,
        color: '#007bff',
        fillColor: '#007bff',
        fillOpacity: 0.1,
        weight: 1
    }).addTo(map);

    userMarker.bindPopup(`
        <div style="text-align: center;">
            <strong>📍 Votre position</strong><br>
            Précision: ${accuracy.toFixed(0)}m
        </div>
    `);
}

// Charger les structures depuis la base de données
async function loadNearbyStructures(lat, lng) {
    console.log('🔄 Chargement des structures depuis la base de données...');

    try {
        // Charger les vraies données depuis l'API
        const response = await fetch('api/get_structures.php');
        const data = await response.json();

        if (data.success && data.structures && data.structures.length > 0) {
            // Convertir les données de la DB au format attendu par la carte
            structures = data.structures.map(structure => ({
                id: structure.id,
                name: structure.nom,
                type: structure.type_nom.toLowerCase(),
                lat: parseFloat(structure.latitude),
                lng: parseFloat(structure.longitude),
                description: structure.description || `${structure.type_nom} - Capacité: ${structure.capacite_actuelle}/${structure.capacite_max}`,
                adresse: structure.adresse,
                telephone: structure.telephone,
                email: structure.email,
                responsable: structure.responsable,
                capacite_max: structure.capacite_max,
                capacite_actuelle: structure.capacite_actuelle,
                urgence: structure.urgence || false
            }));

            console.log(`✅ ${structures.length} structures chargées depuis la base de données`);

            // Afficher les détails des structures chargées
            structures.forEach(structure => {
                console.log(`📍 ${structure.name} (${structure.type}) - ${structure.adresse}`);
            });

            addStructuresToMap();

            // Mettre à jour l'indicateur de statut
            updateLocationIndicator('success', `${structures.length} structures trouvées`);

        } else {
            console.warn('⚠️ Aucune structure trouvée dans la base de données');
            // Fallback avec données réelles de la DB
            loadFallbackStructures();
        }
    } catch (error) {
        console.error('❌ Erreur lors du chargement des structures:', error);
        // Fallback avec données réelles de la DB
        loadFallbackStructures();
    }
}

// Charger des structures de fallback basées sur les vraies données de la DB
function loadFallbackStructures() {
    console.log('🔄 Chargement des structures de fallback (données réelles de la DB)...');

    // Utiliser les vraies données de la base comme fallback avec coordonnées corrigées
    structures = [
        {
            id: 2,
            name: "orphelinat igikundiro",
            type: "orphelinat",
            lat: -3.3614, // Corrigé vers Bujumbura
            lng: 29.3599,
            description: "Accueil d'urgence pour enfants en détresse - Capacité: 39/42",
            adresse: "jhqwe",
            telephone: "5267i82",
            email: "<EMAIL>",
            responsable: "so vivi",
            capacite_max: 42,
            capacite_actuelle: 39,
            urgence: false
        },
        {
            id: 3,
            name: "orphelinat ejo heza",
            type: "orphelinat",
            lat: -3.4264, // Corrigé vers Gitega
            lng: 29.9306,
            description: "Accueil d'urgence pour enfants en détresse - Capacité: 52/33 (URGENT)",
            adresse: "kanyosha 3ieme avenue",
            telephone: "795674666",
            email: "<EMAIL>",
            responsable: "soeur divine",
            capacite_max: 33,
            capacite_actuelle: 52,
            urgence: true
        },
        {
            id: 4,
            name: "orphelinat KIRA",
            type: "orphelinat",
            lat: -2.5847, // Corrigé vers Kirundo
            lng: 30.0944,
            description: "Accueil d'urgence pour enfants en détresse - Capacité: 367/265",
            adresse: "kirundo",
            telephone: "76356273",
            email: "<EMAIL>",
            responsable: "karubwenge",
            capacite_max: 265,
            capacite_actuelle: 367,
            urgence: false
        },
        {
            id: 5,
            name: "orphelinat uzobaho",
            type: "orphelinat",
            lat: -3.4264, // Corrigé vers région Mwaro
            lng: 29.9306,
            description: "Accueil d'urgence pour enfants en détresse - Capacité: 21/27",
            adresse: "mwaro",
            telephone: "668732689",
            email: "<EMAIL>",
            responsable: "karorero",
            capacite_max: 27,
            capacite_actuelle: 21,
            urgence: false
        }
    ];

    console.log(`✅ ${structures.length} structures de fallback chargées (données réelles de la DB)`);
    addStructuresToMap();

    // Mettre à jour l'indicateur de statut
    updateLocationIndicator('success', `${structures.length} structures chargées (données locales)`);
}

// Ajouter les structures à la carte
function addStructuresToMap() {
    // Vider le cluster
    markerClusterGroup.clearLayers();

    structures.forEach(structure => {
        const icon = getStructureIcon(structure.type);

        const marker = L.marker([structure.lat, structure.lng], {
            icon: L.divIcon({
                className: 'structure-marker',
                html: `<div style="background: ${icon.color}; color: white; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 16px; border: 2px solid white; box-shadow: 0 2px 6px rgba(0,0,0,0.3);">${icon.emoji}</div>`,
                iconSize: [30, 30],
                iconAnchor: [15, 15]
            })
        });

        // Créer un popup avec les vraies informations de la base de données
        const urgenceClass = structure.urgence ? 'style="color: #dc3545; font-weight: bold;"' : '';
        const capaciteColor = structure.capacite_actuelle > structure.capacite_max ? '#dc3545' : '#28a745';

        marker.bindPopup(`
            <div style="text-align: left; min-width: 250px; font-family: Arial, sans-serif;">
                <h3 style="margin: 0 0 10px 0; color: #333; text-align: center;">${structure.name}</h3>

                <div style="margin-bottom: 10px;">
                    <strong>📍 Adresse:</strong> ${structure.adresse || 'Non spécifiée'}
                </div>

                <div style="margin-bottom: 10px;">
                    <strong>👥 Capacité:</strong>
                    <span style="color: ${capaciteColor}; font-weight: bold;">
                        ${structure.capacite_actuelle}/${structure.capacite_max}
                    </span>
                    ${structure.urgence ? '<span style="color: #dc3545; font-weight: bold;"> ⚠️ URGENT</span>' : ''}
                </div>

                ${structure.responsable ? `<div style="margin-bottom: 10px;"><strong>👤 Responsable:</strong> ${structure.responsable}</div>` : ''}

                ${structure.telephone ? `<div style="margin-bottom: 10px;"><strong>📞 Téléphone:</strong> ${structure.telephone}</div>` : ''}

                ${structure.email ? `<div style="margin-bottom: 10px;"><strong>📧 Email:</strong> ${structure.email}</div>` : ''}

                <div style="margin-bottom: 10px;">
                    <strong>ℹ️ Description:</strong> ${structure.description}
                </div>

                <div style="display: flex; gap: 8px; justify-content: center; margin-top: 15px;">
                    <button onclick="getDirections(${structure.lat}, ${structure.lng})" style="background: #007bff; color: white; border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                        🧭 Itinéraire
                    </button>
                    ${structure.telephone ? `
                        <button onclick="window.open('tel:${structure.telephone}')" style="background: #28a745; color: white; border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                            📞 Appeler
                        </button>
                    ` : ''}
                    ${structure.email ? `
                        <button onclick="window.open('mailto:${structure.email}')" style="background: #6c757d; color: white; border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                            📧 Email
                        </button>
                    ` : ''}
                </div>
            </div>
        `);

        markerClusterGroup.addLayer(marker);
    });
}

// Obtenir l'icône selon le type de structure (basé sur les types de la DB)
function getStructureIcon(type) {
    const icons = {
        // Types de la base de données
        'orphelinat': { emoji: '🏠', color: '#28a745' },
        'ong': { emoji: '🤝', color: '#17a2b8' },
        'centre_encadrement': { emoji: '🏢', color: '#007bff' },
        'famille_accueil': { emoji: '👨‍👩‍👧‍👦', color: '#ffc107' },

        // Types génériques (fallback)
        'centre': { emoji: '�', color: '#007bff' },
        'urgence': { emoji: '🚨', color: '#dc3545' },
        'famille': { emoji: '👨‍👩‍👧‍👦', color: '#ffc107' }
    };

    // Normaliser le type (minuscules, remplacer espaces par underscores)
    const normalizedType = type.toLowerCase().replace(/\s+/g, '_');

    return icons[normalizedType] || icons['centre'];
}

// Mettre à jour l'indicateur de géolocalisation
function updateIndicator(status, text) {
    const indicator = document.getElementById('geolocationIndicator');
    const textElement = document.getElementById('indicatorText');

    // Supprimer toutes les classes de statut
    indicator.classList.remove('loading', 'active', 'error');

    // Ajouter la nouvelle classe
    indicator.classList.add(status);

    // Mettre à jour le texte
    textElement.textContent = text;

    // Mettre à jour l'emoji
    const emoji = indicator.querySelector('span:first-child');
    switch(status) {
        case 'loading':
            emoji.innerHTML = '<span class="pulse">📍</span>';
            break;
        case 'active':
            emoji.innerHTML = '✅';
            break;
        case 'error':
            emoji.innerHTML = '❌';
            break;
    }
}

// Obtenir des directions
function getDirections(lat, lng) {
    if (!userLocation) {
        alert('Veuillez d\'abord obtenir votre position');
        return;
    }

    const url = `https://www.openstreetmap.org/directions?from=${userLocation.lat},${userLocation.lng}&to=${lat},${lng}`;
    window.open(url, '_blank');
}

// Appeler une structure (simulation)
function callStructure() {
    alert('Fonctionnalité d\'appel à implémenter');
}

// Ouvrir les préférences de géolocalisation
function openGeolocationPreferences() {
    if (window.geolocationPreferences) {
        window.geolocationPreferences.openModal();
    } else {
        alert('Interface de préférences non disponible');
    }
}

// Lancer les tests de géolocalisation
function runGeolocationTests() {
    if (window.geolocationTester) {
        window.geolocationTester.runAllTests();
    } else {
        alert('Module de tests non disponible');
    }
}

// Initialisation automatique de la géolocalisation
async function initAutoGeolocation() {
    console.log('🚀 Initialisation géolocalisation automatique...');

    try {
        if (window.autoLocationManager) {
            await window.autoLocationManager.init({
                onLocationFound: (position) => {
                    console.log('✅ Position automatique trouvée:', position);

                    userLocation = {
                        lat: position.latitude,
                        lng: position.longitude,
                        accuracy: position.accuracy
                    };

                    addUserMarker(position.latitude, position.longitude, position.accuracy);
                    map.setView([position.latitude, position.longitude], 12);
                    loadNearbyStructures(position.latitude, position.longitude);

                    updateIndicator('active', `Position automatique (${position.accuracy.toFixed(0)}m)`);
                },
                onLocationError: (error) => {
                    console.warn('⚠️ Erreur géolocalisation automatique:', error);
                    updateIndicator('error', 'Géolocalisation automatique échouée');
                }
            });

            await window.autoLocationManager.startAutoDetection();
        } else {
            // Fallback vers géolocalisation manuelle
            setTimeout(() => requestUserLocation(), 1000);
        }
    } catch (error) {
        console.error('❌ Erreur initialisation automatique:', error);
        // Fallback vers géolocalisation manuelle
        setTimeout(() => requestUserLocation(), 1000);
    }
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', () => {
    console.log('🌍 Initialisation de la page home.php...');

    // Initialiser la carte
    initMap();

    // Afficher le bouton de test en mode développement
    if (window.location.hostname === 'localhost') {
        document.getElementById('testBtn').style.display = 'block';
    }

    // Démarrer la géolocalisation automatique après un court délai
    setTimeout(() => {
        initAutoGeolocation();
    }, 500);
});
</script>

</body>
</html>
