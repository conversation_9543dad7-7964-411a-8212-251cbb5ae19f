<?php
$pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

$idParrain = $_GET['id_parrain'] ?? null;
if (!$idParrain) {
    echo json_encode([]);
    exit;
}

$sql = "
    SELECT e.nom, e.matricule, s.nom AS structure
    FROM demande_parrainage dp
    JOIN enfant e ON dp.enfant_id = e.id
    JOIN structure s ON e.structure_id = s.id
    WHERE dp.parrain_id = ? AND dp.statut = 'en_attente'
";
$stmt = $pdo->prepare($sql);
$stmt->execute([$idParrain]);
$enfants = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo json_encode($enfants);
