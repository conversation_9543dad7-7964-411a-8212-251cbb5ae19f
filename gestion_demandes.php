<?php
session_start();
$pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

// Obtenir les parrains avec au moins une demande en attente
$sql = "
    SELECT 
        p.id AS parrain_id,
        p.nom AS nom_parrain,
        p.prenom AS prenom_parrain,
        p.email,
        p.telephone,
        p.adresse,
        p.photo_cni,
        p.historique_bancaire,
        MAX(dp.id) AS demande_id
    FROM demande_parrainage dp
    JOIN parrain p ON dp.parrain_id = p.id
    WHERE dp.statut = 'en_attente'
    GROUP BY p.id
";
$demandes = $pdo->query($sql)->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Gestion des Demandes de Parrainage</title>
    <style>
        body {
            margin: 0;
            font-family: 'Segoe UI', sans-serif;
            background-color: #f9f9f9;
        }
        header {
            background-color: #007bff;
            color: white;
            padding: 20px 30px;
        }
        .container {
            display: flex;
            padding: 20px;
        }
        .liste {
            width: 70%;
            padding-right: 20px;
        }
        .details {
            width: 30%;
            background-color: #ffffff;
            border-left: 2px solid #ccc;
            padding: 20px;
            overflow-y: auto;
            max-height: 80vh;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
        table th, table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        table th {
            background-color: #007bff;
            color: white;
        }
        tr:hover {
            background-color: #f1f1f1;
        }
        button {
            padding: 6px 10px;
            margin: 2px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .afficher-btn { background-color: #17a2b8; color: white; }
        .valide-btn { background-color: #28a745; color: white; }
        .refuse-btn { background-color: #dc3545; color: white; }
        img {
            max-width: 100%;
            height: auto;
            margin-top: 10px;
        }
        a.pdf-link {
            color: #007bff;
            text-decoration: none;
        }
        a.pdf-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>

<header>
    <h2>📂 Gestion des Demandes de Parrainage</h2>
</header>

<div class="container">
    <div class="liste">
        <table>
            <thead>
                <tr>
                    <th>Nom du Parrain</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($demandes as $d): ?>
                    <tr>
                        <td><?= htmlspecialchars($d['nom_parrain'] . ' ' . $d['prenom_parrain']) ?></td>
                        <td>
                            <button class="afficher-btn" onclick='afficherDetails(<?= json_encode($d) ?>)'>Afficher</button>
                            <form method="post" action="traiter_demande.php" style="display:inline;">
                                <input type="hidden" name="demande_id" value="<?= $d['demande_id'] ?>">
                                <button type="submit" name="action" value="valide" class="valide-btn">Valider</button>
                                <button type="submit" name="action" value="refuse" class="refuse-btn">Refuser</button>
                            </form>
                        </td>
                    </tr>
                <?php endforeach; ?>
                <?php if (empty($demandes)): ?>
                    <tr><td colspan="2">Aucune demande en attente.</td></tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <div class="details">
        <h3>Détails du Parrain</h3>
        <div id="infos-parrain">
            Cliquez sur <strong>"Afficher"</strong> pour voir les détails ici.
        </div>
    </div>
</div>

<script>
function afficherDetails(data) {
    fetch('get_enfants_parrain.php?id_parrain=' + data.parrain_id)
        .then(response => response.json())
        .then(enfants => {
            let enfantsHtml = '';
            enfants.forEach(e => {
                enfantsHtml += `
                    <li><strong>Nom :</strong> ${e.nom} <br>
                        <strong>Matricule :</strong> ${e.matricule} <br>
                        <strong>Structure :</strong> ${e.structure}
                    </li><br>`;
            });

            const html = `
                <h4>🧍 Parrain</h4>
                <p><strong>Nom :</strong> ${data.nom_parrain}</p>
                <p><strong>Prénom :</strong> ${data.prenom_parrain}</p>
                <p><strong>Email :</strong> ${data.email}</p>
                <p><strong>Adresse :</strong> ${data.adresse}</p>
                ${data.photo_cni ? `<p><strong>Photo CNI :</strong><br><img src="${data.photo_cni}" alt="CNI du parrain"></p>` : ''}
                ${data.historique_bancaire ? `<p><strong>Situation bancaire :</strong><br><a class="pdf-link" href="${data.historique_bancaire}" target="_blank">Voir le document PDF</a></p>` : ''}
                <hr>
                <h4>👶 Enfants en demande</h4>
                <ul>${enfantsHtml}</ul>
            `;
            document.getElementById('infos-parrain').innerHTML = html;
        })
        .catch(error => {
            document.getElementById('infos-parrain').innerHTML = 'Erreur lors du chargement des enfants.';
            console.error(error);
        });
}
</script>

</body>
</html>
