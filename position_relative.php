<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ma Position vs Centres - Umwana Voice</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <!-- Leaflet MarkerCluster CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        .map-container {
            height: 600px;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #e5e7eb;
        }
        
        .distance-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .distance-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .distance-very-close { border-left: 4px solid #10b981; }
        .distance-close { border-left: 4px solid #3b82f6; }
        .distance-medium { border-left: 4px solid #f59e0b; }
        .distance-far { border-left: 4px solid #ef4444; }
        
        .control-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1000;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 12px;
            min-width: 200px;
        }
        
        .user-info {
            position: absolute;
            bottom: 10px;
            left: 10px;
            z-index: 1000;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 12px;
            max-width: 300px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8 max-w-7xl">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="mb-6">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">
                    📍 Ma Position par Rapport aux Centres
                </h1>
                <p class="text-gray-600">
                    Visualisez votre position et trouvez les centres les plus proches de vous
                </p>
            </div>

            <!-- Alertes -->
            <div id="alertContainer" class="mb-6 hidden">
                <div id="alertBox" class="p-4 rounded-lg border-l-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <span id="alertIcon" class="text-xl"></span>
                        </div>
                        <div class="ml-3">
                            <p id="alertTitle" class="text-sm font-medium"></p>
                            <p id="alertMessage" class="text-sm mt-1"></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Boutons de contrôle -->
            <div class="flex flex-wrap gap-4 mb-6">
                <button id="btnLocateMe" 
                        class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg font-medium">
                    📍 Me Localiser
                </button>
                <button id="btnToggleDistances" 
                        class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg font-medium">
                    📏 Afficher Distances
                </button>
                <button id="btnToggleRadius" 
                        class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-2 rounded-lg font-medium">
                    🎯 Rayon de Recherche
                </button>
                <button id="btnFindNearest" 
                        class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg font-medium">
                    🚀 Centre le Plus Proche
                </button>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Carte -->
                <div class="lg:col-span-2">
                    <div class="relative">
                        <div id="map" class="map-container"></div>
                        
                        <!-- Panneau de contrôle sur la carte -->
                        <div class="control-panel">
                            <h3 class="font-bold text-sm mb-2">Contrôles</h3>
                            <div class="space-y-2">
                                <label class="flex items-center text-sm">
                                    <input type="checkbox" id="showConnections" class="mr-2">
                                    Lignes de connexion
                                </label>
                                <label class="flex items-center text-sm">
                                    <input type="checkbox" id="showRadius" class="mr-2">
                                    Zones de couverture
                                </label>
                                <label class="flex items-center text-sm">
                                    <input type="checkbox" id="showUrgentOnly" class="mr-2">
                                    Urgences seulement
                                </label>
                            </div>
                            
                            <div class="mt-3">
                                <label class="text-xs text-gray-600">Rayon de recherche (km)</label>
                                <input type="range" id="searchRadius" min="1" max="50" value="10" 
                                       class="w-full mt-1">
                                <span id="radiusValue" class="text-xs text-gray-500">10 km</span>
                            </div>
                        </div>

                        <!-- Informations utilisateur -->
                        <div class="user-info hidden" id="userInfo">
                            <h3 class="font-bold text-sm mb-2">📍 Votre Position</h3>
                            <div class="text-xs space-y-1">
                                <div>Latitude: <span id="userLat" class="font-mono">--</span></div>
                                <div>Longitude: <span id="userLng" class="font-mono">--</span></div>
                                <div>Précision: <span id="userAccuracy">--</span></div>
                                <div>Centres dans le rayon: <span id="centresCount">--</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Liste des centres par distance -->
                <div class="lg:col-span-1">
                    <h2 class="text-xl font-bold mb-4">🏥 Centres par Distance</h2>
                    
                    <!-- État de chargement -->
                    <div id="loadingCentres" class="text-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                        <p class="text-gray-500">Chargement des centres...</p>
                    </div>

                    <!-- Message si pas de position -->
                    <div id="noPosition" class="text-center py-8 hidden">
                        <div class="text-4xl mb-2">📍</div>
                        <p class="text-gray-500 mb-4">Localisez-vous d'abord pour voir les distances</p>
                        <button onclick="document.getElementById('btnLocateMe').click()" 
                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                            Me Localiser
                        </button>
                    </div>

                    <!-- Liste des centres -->
                    <div id="centresList" class="space-y-3 max-h-96 overflow-y-auto hidden">
                        <!-- Les centres seront ajoutés ici -->
                    </div>

                    <!-- Statistiques -->
                    <div id="distanceStats" class="mt-6 p-4 bg-gray-50 rounded-lg hidden">
                        <h3 class="font-bold text-sm mb-2">📊 Statistiques</h3>
                        <div class="text-xs space-y-1">
                            <div>Centre le plus proche: <span id="nearestDistance" class="font-bold">--</span></div>
                            <div>Centre le plus éloigné: <span id="farthestDistance" class="font-bold">--</span></div>
                            <div>Distance moyenne: <span id="averageDistance" class="font-bold">--</span></div>
                            <div>Centres d'urgence proches: <span id="urgentNearby" class="font-bold text-red-600">--</span></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>
    <script src="js/precise-geolocation.js"></script>
    <script>
        // Variables globales
        let map;
        let userLocation = null;
        let userMarker = null;
        let centres = [];
        let centreMarkers = [];
        let connectionLines = [];
        let coverageCircles = [];
        let searchRadiusCircle = null;
        let preciseGeo;
        let markerClusterGroup;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
            initGeolocation();
            initEventListeners();
            loadCentres();
        });

        // Initialiser la carte
        function initMap() {
            map = L.map('map').setView([-3.3614, 29.3599], 12);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);
            
            // Initialiser le clustering
            markerClusterGroup = L.markerClusterGroup({
                maxClusterRadius: 50,
                spiderfyOnMaxZoom: true,
                showCoverageOnHover: false
            });
            map.addLayer(markerClusterGroup);
        }

        // Initialiser la géolocalisation
        function initGeolocation() {
            preciseGeo = new PreciseGeolocation();
        }

        // Initialiser les événements
        function initEventListeners() {
            document.getElementById('btnLocateMe').addEventListener('click', locateUser);
            document.getElementById('btnToggleDistances').addEventListener('click', toggleDistanceLines);
            document.getElementById('btnToggleRadius').addEventListener('click', toggleSearchRadius);
            document.getElementById('btnFindNearest').addEventListener('click', findNearestCentre);

            document.getElementById('showConnections').addEventListener('change', updateConnectionLines);
            document.getElementById('showRadius').addEventListener('change', updateCoverageCircles);
            document.getElementById('showUrgentOnly').addEventListener('change', filterCentres);
            document.getElementById('searchRadius').addEventListener('input', updateSearchRadius);
        }

        // Charger les centres
        async function loadCentres() {
            try {
                const response = await fetch('api/get_structures.php');
                const data = await response.json();

                if (data.success) {
                    centres = data.structures;
                    displayCentresOnMap();
                    showAlert('success', 'Centres chargés', `${data.count} centres disponibles`);
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                showAlert('error', 'Erreur de chargement', 'Impossible de charger les centres');
                console.error('Erreur:', error);
            }

            document.getElementById('loadingCentres').classList.add('hidden');
            document.getElementById('noPosition').classList.remove('hidden');
        }

        // Localiser l'utilisateur
        function locateUser() {
            const btn = document.getElementById('btnLocateMe');
            btn.innerHTML = '⏳ Localisation...';
            btn.disabled = true;

            preciseGeo.getPreciseLocation({
                onSuccess: (position) => {
                    userLocation = {
                        lat: position.latitude,
                        lng: position.longitude,
                        accuracy: position.accuracy
                    };

                    updateUserMarker();
                    updateUserInfo();
                    calculateDistances();
                    updateAllVisualizations();

                    btn.innerHTML = '✅ Position Trouvée';
                    setTimeout(() => {
                        btn.innerHTML = '📍 Me Localiser';
                        btn.disabled = false;
                    }, 2000);

                    showAlert('success', 'Position trouvée',
                        `Localisé avec une précision de ${position.accuracy.toFixed(0)}m`);
                },
                onError: (error) => {
                    btn.innerHTML = '📍 Me Localiser';
                    btn.disabled = false;
                    showAlert('error', 'Erreur de localisation', error.message);
                },
                onProgress: (progress) => {
                    btn.innerHTML = `⏳ ${progress.message}`;
                }
            });
        }

        // Mettre à jour le marqueur utilisateur
        function updateUserMarker() {
            if (userMarker) {
                map.removeLayer(userMarker);
            }

            userMarker = L.marker([userLocation.lat, userLocation.lng], {
                icon: L.icon({
                    iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-red.png',
                    shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
                    iconSize: [25, 41],
                    iconAnchor: [12, 41],
                    popupAnchor: [1, -34],
                    shadowSize: [41, 41]
                })
            }).addTo(map);

            userMarker.bindPopup(`
                <strong>🏠 Votre Position</strong><br>
                Latitude: ${userLocation.lat.toFixed(6)}<br>
                Longitude: ${userLocation.lng.toFixed(6)}<br>
                Précision: ±${userLocation.accuracy.toFixed(0)}m
            `);

            // Ajouter cercle de précision
            if (userLocation.accuracy < 200) {
                L.circle([userLocation.lat, userLocation.lng], {
                    radius: userLocation.accuracy,
                    color: '#ef4444',
                    fillColor: '#ef4444',
                    fillOpacity: 0.1,
                    weight: 1
                }).addTo(map);
            }

            map.setView([userLocation.lat, userLocation.lng], 13);
        }

        // Mettre à jour les informations utilisateur
        function updateUserInfo() {
            document.getElementById('userLat').textContent = userLocation.lat.toFixed(6);
            document.getElementById('userLng').textContent = userLocation.lng.toFixed(6);
            document.getElementById('userAccuracy').textContent = `±${userLocation.accuracy.toFixed(0)}m`;
            document.getElementById('userInfo').classList.remove('hidden');
        }

        // Calculer les distances
        function calculateDistances() {
            if (!userLocation) return;

            centres.forEach(centre => {
                centre.distance = calculateDistance(
                    userLocation.lat, userLocation.lng,
                    parseFloat(centre.latitude), parseFloat(centre.longitude)
                );
            });

            // Trier par distance
            centres.sort((a, b) => {
                if (a.urgence && !b.urgence) return -1;
                if (!a.urgence && b.urgence) return 1;
                return a.distance - b.distance;
            });

            updateCentresList();
            updateDistanceStats();
        }

        // Mettre à jour la liste des centres
        function updateCentresList() {
            const container = document.getElementById('centresList');
            const searchRadius = parseInt(document.getElementById('searchRadius').value);
            const showUrgentOnly = document.getElementById('showUrgentOnly').checked;

            let filteredCentres = centres.filter(centre => {
                if (centre.distance > searchRadius) return false;
                if (showUrgentOnly && !centre.urgence) return false;
                return true;
            });

            document.getElementById('centresCount').textContent = filteredCentres.length;

            if (filteredCentres.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-center py-4">Aucun centre dans le rayon sélectionné</p>';
                document.getElementById('centresList').classList.remove('hidden');
                document.getElementById('noPosition').classList.add('hidden');
                return;
            }

            const centresHtml = filteredCentres.map(centre => {
                const distanceClass = getDistanceClass(centre.distance);
                const distanceIcon = getDistanceIcon(centre.distance);
                const urgenceIcon = centre.urgence ? '🚨' : '';

                return `
                    <div class="distance-card ${distanceClass} bg-white p-3 rounded-lg border cursor-pointer"
                         onclick="focusOnCentre(${centre.latitude}, ${centre.longitude}, '${centre.nom}')">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="font-bold text-sm">${urgenceIcon} ${centre.nom}</h3>
                            <span class="text-xs bg-gray-100 px-2 py-1 rounded">${centre.type_nom}</span>
                        </div>

                        <div class="text-xs text-gray-600 mb-2">
                            📍 ${centre.adresse}
                        </div>

                        <div class="flex justify-between items-center">
                            <div class="text-sm font-bold text-blue-600">
                                ${distanceIcon} ${centre.distance.toFixed(1)} km
                            </div>
                            <div class="text-xs text-gray-500">
                                ${centre.capacite_actuelle}/${centre.capacite_max} places
                            </div>
                        </div>

                        <div class="mt-2 flex gap-2">
                            <a href="tel:${centre.telephone}"
                               class="bg-green-500 hover:bg-green-600 text-white px-2 py-1 rounded text-xs">
                                📞 Appeler
                            </a>
                            <button onclick="event.stopPropagation(); showRoute(${centre.latitude}, ${centre.longitude})"
                                    class="bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded text-xs">
                                🗺️ Itinéraire
                            </button>
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML = centresHtml;
            document.getElementById('centresList').classList.remove('hidden');
            document.getElementById('noPosition').classList.add('hidden');
        }

        // Afficher les centres sur la carte
        function displayCentresOnMap() {
            markerClusterGroup.clearLayers();

            centres.forEach(centre => {
                const lat = parseFloat(centre.latitude);
                const lng = parseFloat(centre.longitude);

                const marker = L.marker([lat, lng]);

                // Icône selon le type et l'urgence
                if (centre.urgence) {
                    marker.setIcon(L.icon({
                        iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-red.png',
                        shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
                        iconSize: [25, 41],
                        iconAnchor: [12, 41],
                        popupAnchor: [1, -34],
                        shadowSize: [41, 41]
                    }));
                }

                const popupContent = `
                    <strong>${centre.urgence ? '🚨' : '🏥'} ${centre.nom}</strong><br>
                    <em>${centre.type_nom}</em><br>
                    📍 ${centre.adresse}<br>
                    📞 ${centre.telephone}<br>
                    👥 ${centre.capacite_actuelle}/${centre.capacite_max} places
                    ${centre.distance ? `<br>📏 Distance: ${centre.distance.toFixed(1)} km` : ''}
                `;

                marker.bindPopup(popupContent);
                markerClusterGroup.addLayer(marker);
            });
        }

        // Mettre à jour les statistiques de distance
        function updateDistanceStats() {
            if (!userLocation || centres.length === 0) return;

            const distances = centres.map(c => c.distance).filter(d => d !== undefined);
            const urgentNearby = centres.filter(c => c.urgence && c.distance <= 5).length;

            document.getElementById('nearestDistance').textContent = `${Math.min(...distances).toFixed(1)} km`;
            document.getElementById('farthestDistance').textContent = `${Math.max(...distances).toFixed(1)} km`;
            document.getElementById('averageDistance').textContent = `${(distances.reduce((a, b) => a + b, 0) / distances.length).toFixed(1)} km`;
            document.getElementById('urgentNearby').textContent = urgentNearby;

            document.getElementById('distanceStats').classList.remove('hidden');
        }

        // Obtenir la classe CSS selon la distance
        function getDistanceClass(distance) {
            if (distance <= 2) return 'distance-very-close';
            if (distance <= 5) return 'distance-close';
            if (distance <= 10) return 'distance-medium';
            return 'distance-far';
        }

        // Obtenir l'icône selon la distance
        function getDistanceIcon(distance) {
            if (distance <= 2) return '🟢';
            if (distance <= 5) return '🔵';
            if (distance <= 10) return '🟡';
            return '🔴';
        }

        // Centrer sur un centre
        function focusOnCentre(lat, lng, name) {
            map.setView([lat, lng], 16);
            showAlert('info', 'Centre sélectionné', `Centré sur ${name}`);
        }

        // Afficher l'itinéraire
        function showRoute(destLat, destLng) {
            if (!userLocation) {
                showAlert('error', 'Position requise', 'Localisez-vous d\'abord');
                return;
            }

            // Ouvrir dans Google Maps ou autre service de navigation
            const url = `https://www.google.com/maps/dir/${userLocation.lat},${userLocation.lng}/${destLat},${destLng}`;
            window.open(url, '_blank');
        }

        // Basculer les lignes de distance
        function toggleDistanceLines() {
            const btn = document.getElementById('btnToggleDistances');
            const checkbox = document.getElementById('showConnections');

            checkbox.checked = !checkbox.checked;
            updateConnectionLines();

            btn.textContent = checkbox.checked ? '📏 Masquer Distances' : '📏 Afficher Distances';
        }

        // Mettre à jour les lignes de connexion
        function updateConnectionLines() {
            // Supprimer les anciennes lignes
            connectionLines.forEach(line => map.removeLayer(line));
            connectionLines = [];

            if (!userLocation || !document.getElementById('showConnections').checked) return;

            const searchRadius = parseInt(document.getElementById('searchRadius').value);
            const nearbyStructures = centres.filter(centre => centre.distance <= searchRadius).slice(0, 10);

            nearbyStructures.forEach(centre => {
                const line = L.polyline([
                    [userLocation.lat, userLocation.lng],
                    [parseFloat(centre.latitude), parseFloat(centre.longitude)]
                ], {
                    color: centre.urgence ? '#ef4444' : '#3b82f6',
                    weight: 2,
                    opacity: 0.7,
                    dashArray: '5, 10'
                }).addTo(map);

                line.bindTooltip(`${centre.nom}<br>${centre.distance.toFixed(1)} km`, {
                    permanent: false,
                    direction: 'center'
                });

                connectionLines.push(line);
            });
        }

        // Basculer le rayon de recherche
        function toggleSearchRadius() {
            const btn = document.getElementById('btnToggleRadius');

            if (searchRadiusCircle) {
                map.removeLayer(searchRadiusCircle);
                searchRadiusCircle = null;
                btn.textContent = '🎯 Afficher Rayon';
            } else {
                updateSearchRadiusCircle();
                btn.textContent = '🎯 Masquer Rayon';
            }
        }

        // Mettre à jour le rayon de recherche
        function updateSearchRadius() {
            const radius = parseInt(document.getElementById('searchRadius').value);
            document.getElementById('radiusValue').textContent = `${radius} km`;

            if (userLocation) {
                calculateDistances();
                updateSearchRadiusCircle();
            }
        }

        // Mettre à jour le cercle de rayon de recherche
        function updateSearchRadiusCircle() {
            if (!userLocation) return;

            if (searchRadiusCircle) {
                map.removeLayer(searchRadiusCircle);
            }

            const radius = parseInt(document.getElementById('searchRadius').value) * 1000; // Convertir en mètres

            searchRadiusCircle = L.circle([userLocation.lat, userLocation.lng], {
                radius: radius,
                color: '#8b5cf6',
                fillColor: '#8b5cf6',
                fillOpacity: 0.1,
                weight: 2,
                dashArray: '10, 5'
            }).addTo(map);

            searchRadiusCircle.bindTooltip(`Rayon de recherche: ${radius/1000} km`, {
                permanent: false,
                direction: 'center'
            });
        }

        // Trouver le centre le plus proche
        function findNearestCentre() {
            if (!userLocation || centres.length === 0) {
                showAlert('error', 'Données manquantes', 'Localisez-vous et chargez les centres d\'abord');
                return;
            }

            const nearest = centres[0]; // Déjà trié par distance

            map.setView([parseFloat(nearest.latitude), parseFloat(nearest.longitude)], 16);

            showAlert('success', 'Centre le plus proche',
                `${nearest.nom} à ${nearest.distance.toFixed(1)} km`);
        }

        // Filtrer les centres
        function filterCentres() {
            if (userLocation) {
                updateCentresList();
                updateAllVisualizations();
            }
        }

        // Mettre à jour toutes les visualisations
        function updateAllVisualizations() {
            updateConnectionLines();
            updateCoverageCircles();
            updateSearchRadiusCircle();
        }

        // Mettre à jour les cercles de couverture
        function updateCoverageCircles() {
            // Supprimer les anciens cercles
            coverageCircles.forEach(circle => map.removeLayer(circle));
            coverageCircles = [];

            if (!document.getElementById('showRadius').checked) return;

            centres.forEach(centre => {
                let radius = 2000; // 2km par défaut
                if (centre.type_nom === 'ORPHELINAT') radius = 3000;
                if (centre.urgence) radius = 5000;

                const circle = L.circle([parseFloat(centre.latitude), parseFloat(centre.longitude)], {
                    radius: radius,
                    color: centre.urgence ? '#ef4444' : '#3b82f6',
                    fillColor: centre.urgence ? '#ef4444' : '#3b82f6',
                    fillOpacity: 0.1,
                    weight: 1,
                    opacity: 0.3
                }).addTo(map);

                circle.bindTooltip(`Zone de couverture: ${centre.nom}<br>Rayon: ${(radius/1000).toFixed(1)} km`);
                coverageCircles.push(circle);
            });
        }

        // Calculer la distance entre deux points
        function calculateDistance(lat1, lng1, lat2, lng2) {
            const R = 6371; // Rayon de la Terre en km
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLng = (lng2 - lng1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                      Math.sin(dLng/2) * Math.sin(dLng/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }

        // Afficher une alerte
        function showAlert(type, title, message) {
            const container = document.getElementById('alertContainer');
            const box = document.getElementById('alertBox');
            const icon = document.getElementById('alertIcon');
            const titleEl = document.getElementById('alertTitle');
            const messageEl = document.getElementById('alertMessage');

            const configs = {
                success: { icon: '✅', classes: 'bg-green-50 border-green-400 text-green-800' },
                error: { icon: '❌', classes: 'bg-red-50 border-red-400 text-red-800' },
                info: { icon: 'ℹ️', classes: 'bg-blue-50 border-blue-400 text-blue-800' }
            };

            const config = configs[type] || configs.info;

            icon.textContent = config.icon;
            titleEl.textContent = title;
            messageEl.textContent = message;
            box.className = `p-4 rounded-lg border-l-4 ${config.classes}`;

            container.classList.remove('hidden');

            setTimeout(() => container.classList.add('hidden'), 5000);
        }
    </script>
</body>
</html>
