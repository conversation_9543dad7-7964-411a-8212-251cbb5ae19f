<?php
// Connexion à la base de données avec PDO
try {
    $pdo = new PDO("mysql:host=localhost;dbname=gestion_enfant", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $id = $_POST['id'];
        $nom = $_POST['nom'];
        $prenom = $_POST['prenom'];
        $centre_id = $_POST['centre_id'];

        // Mise à jour dans la base de données
        $stmt = $pdo->prepare("UPDATE enfant SET nom = ?, prenom = ?, centre_id = ? WHERE id = ?");
        $stmt->execute([$nom, $prenom, $centre_id, $id]);

        echo "<script>alert('Enfant modifié avec succès !'); window.location.href='gestion_enfants.php';</script>";
    }

    // Récupérer les données de l'enfant
    $id = $_GET['id'];
    $stmt = $pdo->prepare("SELECT * FROM enfant WHERE id = ?");
    $stmt->execute([$id]);
    $enfant = $stmt->fetch(PDO::FETCH_ASSOC);

    // Récupérer les centres pour le formulaire
    $centres = $pdo->query("SELECT * FROM structure WHERE type_structure_id = 4")->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    echo "Erreur de connexion : " . $e->getMessage();
}
?>

<?php include 'header.php'; ?>

<div class="container">
    <h2>Modifier un Enfant</h2>
    <form method="POST">
        <input type="hidden" name="id" value="<?php echo htmlspecialchars($enfant['id']); ?>">
        <div class="form-group">
            <label for="nom">Nom:</label>
            <input type="text" id="nom" name="nom" value="<?php echo htmlspecialchars($enfant['nom']); ?>" required>
        </div>
        <div class="form-group">
            <label for="prenom">Prénom:</label>
            <input type="text" id="prenom" name="prenom" value="<?php echo htmlspecialchars($enfant['prenom']); ?>" required>
        </div>
        <div class="form-group">
            <label for="centre_id">Centre:</label>
            <select id="centre_id" name="centre_id" required>
                <?php foreach ($centres as $centre): ?>
                    <option value="<?php echo $centre['id']; ?>" <?php echo $centre['id'] == $enfant['centre_id'] ? 'selected' : ''; ?>><?php echo htmlspecialchars($centre['nom']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <button type="submit" class="btn-submit">Modifier</button>
    </form>
</div>