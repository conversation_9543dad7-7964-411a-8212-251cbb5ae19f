<?php
$message = "";

if ($_SERVER["REQUEST_METHOD"] === "POST") {
    try {
        $pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');

        // Données du formulaire
        $nom = $_POST['nom'];
        $prenom = $_POST['prenom'];
        $email = $_POST['email'];
        $telephone = $_POST['telephone'];
        $adresse = $_POST['adresse'];
        $numero_cni = $_POST['numero_cni'];
        $quartier = $_POST['quartier_residence'];
        $zone = $_POST['zone'];
        $commune = $_POST['commune'];
        $province = $_POST['province'];
        $statut = $_POST['statut_familial'];
        $mot_de_passe = password_hash($_POST['mot_de_passe'], PASSWORD_DEFAULT); // hachage

        // Photo CNI
        $photo_path = '';
        if (isset($_FILES['photo_cni']) && $_FILES['photo_cni']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = 'uploads/';
            if (!is_dir($upload_dir)) mkdir($upload_dir, 0777, true);
            $filename = uniqid() . '_' . basename($_FILES['photo_cni']['name']);
            $target_file = $upload_dir . $filename;
            if (move_uploaded_file($_FILES['photo_cni']['tmp_name'], $target_file)) {
                $photo_path = $target_file;
            }
        }

        // Historique bancaire
        $historique_path = '';
        if (isset($_FILES['historique_bancaire']) && $_FILES['historique_bancaire']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = 'uploads/';
            if (!is_dir($upload_dir)) mkdir($upload_dir, 0777, true);
            $filename = uniqid() . '_' . basename($_FILES['historique_bancaire']['name']);
            $target_file = $upload_dir . $filename;
            if (move_uploaded_file($_FILES['historique_bancaire']['tmp_name'], $target_file)) {
                $historique_path = $target_file;
            }
        }

        // Insertion en base
        $stmt = $pdo->prepare("INSERT INTO parrain 
            (nom, prenom, email, telephone, adresse, numero_cni, photo_cni, quartier_residence, zone, commune, province, statut_familial, historique_bancaire, mot_de_passe)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

        $success = $stmt->execute([
            $nom, $prenom, $email, $telephone, $adresse,
            $numero_cni, $photo_path, $quartier, $zone,
            $commune, $province, $statut, $historique_path, $mot_de_passe
        ]);

        if ($success) {
            header("Location: connexion_parrain.php");
            exit();
        } else {
            $message = "<div class='error'>❌ Erreur lors de l'enregistrement.</div>";
        }

    } catch (PDOException $e) {
        $message = "<div class='error'>Erreur de connexion : " . $e->getMessage() . "</div>";
    }
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Ajouter un Parrain</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            background: #f4f6fb;
            font-family: 'Poppins', sans-serif;
            padding: 40px;
            color: #333;
        }

        .form-container {
            max-width: 700px;
            margin: auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        h2 {
            text-align: center;
            color: #1e3d59;
        }

        label {
            font-weight: 600;
        }

        input[type="text"],
        input[type="email"],
        input[type="file"],
        input[type="password"],
        select {
            width: 100%;
            padding: 10px;
            margin-top: 6px;
            margin-bottom: 16px;
            border: 1px solid #ccc;
            border-radius: 8px;
            font-size: 15px;
        }

        button {
            background-color: #1e3d59;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
        }

        button:hover {
            background-color: #2a4f70;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            text-align: center;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>

<div class="form-container">
    <h2>Enregistrement d'un Parrain</h2>

    <?= $message ?>

    <form action="" method="POST" enctype="multipart/form-data">
        <label>Nom:</label>
        <input type="text" name="nom" required>

        <label>Prénom:</label>
        <input type="text" name="prenom" required>

        <label>Email:</label>
        <input type="email" name="email">

        <label>Mot de passe:</label>
        <input type="password" name="mot_de_passe" required>

        <label>Numéro de téléphone:</label>
        <input type="text" name="telephone">

        <label>Adresse:</label>
        <input type="text" name="adresse">

        <label>Numéro de CNI:</label>
        <input type="text" name="numero_cni" required>

        <label>Photo de la CNI (image):</label>
        <input type="file" name="photo_cni" accept="image/*" required>

        <label>Quartier de résidence:</label>
        <input type="text" name="quartier_residence">

        <label>Zone:</label>
        <input type="text" name="zone">

        <label>Commune:</label>
        <input type="text" name="commune">

        <label>Province:</label>
        <input type="text" name="province">

        <label>Statut familial:</label>
        <select name="statut_familial" required>
            <option value="">-- Choisissez --</option>
            <option value="Célibataire">Célibataire</option>
            <option value="Marié(e)">Marié(e)</option>
            <option value="Fille mère">Fille mère</option>
            <option value="Parent célibataire">Parent célibataire</option>
        </select>

        <label>Historique bancaire (fichier PDF ou image) :</label>
        <input type="file" name="historique_bancaire" accept=".pdf,.doc,.docx,.jpg,.png" required>

        <button type="submit">Enregistrer</button>
    </form>
</div>

</body>
</html>
