<?php
// Connexion à la base de données
$pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');

// Requête pour récupérer les projets avec les infos liées
$sql = "
SELECT 
    p.id,
    p.titre,
    p.description,
    p.objectif_financier,
    p.montant_collecte,
    p.date_debut,
    p.date_fin,
    p.documents_joints,
    s.nom AS structure,
    sp.nom AS statut
FROM projet p
LEFT JOIN structure s ON p.structure_id = s.id
LEFT JOIN statut_projet sp ON p.statut_projet_id = sp.id
ORDER BY p.date_debut DESC
";

$projets = $pdo->query($sql)->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Liste des Projets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .pdf-link {
            text-decoration: none;
            color: #007bff;
        }
        .pdf-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body class="bg-light">
<div class="container mt-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Liste des projets enregistrés</h2>
        <a href="ajouter_projet.php" class="btn btn-success">+ Ajouter un projet</a>
    </div>

    <table class="table table-bordered table-hover bg-white shadow">
        <thead class="table-dark text-center">
            <tr>
                <th>Titre</th>
                <th>Description</th>
                <th>Structure</th>
                <th>Statut</th>
                <th>Objectif (€)</th>
                <th>Collecté (€)</th>
                <th>Date début</th>
                <th>Date fin</th>
                <th>Document</th>
            </tr>
        </thead>
        <tbody>
        <?php if (count($projets) > 0): ?>
            <?php foreach ($projets as $projet): ?>
                <tr>
                    <td><?= htmlspecialchars($projet['titre']) ?></td>
                    <td><?= htmlspecialchars($projet['description']) ?></td>
                    <td><?= htmlspecialchars($projet['structure']) ?></td>
                    <td><?= htmlspecialchars($projet['statut']) ?></td>
                    <td class="text-end"><?= number_format($projet['objectif_financier'], 2, ',', ' ') ?></td>
                    <td class="text-end"><?= number_format($projet['montant_collecte'], 2, ',', ' ') ?></td>
                    <td><?= date('d/m/Y', strtotime($projet['date_debut'])) ?></td>
                    <td><?= date('d/m/Y', strtotime($projet['date_fin'])) ?></td>
                    <td class="text-center">
                        <?php if ($projet['documents_joints']): ?>
                            <a href="<?= $projet['documents_joints'] ?>" target="_blank" class="pdf-link">📄 Voir PDF</a>
                        <?php else: ?>
                            <span class="text-muted">Aucun</span>
                        <?php endif; ?>
                    </td>
                </tr>
            <?php endforeach; ?>
        <?php else: ?>
            <tr>
                <td colspan="9" class="text-center text-muted">Aucun projet trouvé.</td>
            </tr>
        <?php endif; ?>
        </tbody>
    </table>
</div>
</body>
</html>
