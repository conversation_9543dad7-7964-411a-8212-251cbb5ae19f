<?php
// Connexion PDO (adaptée comme ci-dessus)
$pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');

// Récupérer les centres existants
$centres = $pdo->query("SELECT id, nom FROM structure WHERE type_structure_id = 1")->fetchAll(PDO::FETCH_ASSOC);

$message = "";
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['prenom'], $_POST['nom'], $_POST['date_naissance'], $_POST['sexe_id'], $_POST['structure_id'])) {
    $stmt = $pdo->prepare("INSERT INTO enfant (prenom, nom, date_naissance, sexe_id, structure_id) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute([
        $_POST['prenom'],
        $_POST['nom'],
        $_POST['date_naissance'],
        $_POST['sexe_id'],
        $_POST['structure_id']
    ]);
    $message = "Enfant enregistré avec succès !";
}
?>

<h2>Enregistrer un enfant</h2>

<?php if ($message): ?>
    <p style="color: green;"><?= htmlspecialchars($message) ?></p>
<?php endif; ?>

<form method="POST">
  <label for="prenom">Prénom</label>
  <input type="text" name="prenom" id="prenom" required>

  <label for="nom">Nom</label>
  <input type="text" name="nom" id="nom" required>

  <label for="date_naissance">Date de naissance</label>
  <input type="date" name="date_naissance" id="date_naissance" required>

  <label>Sexe</label>
  <select name="sexe_id" required>
    <option value="">-- Choisir --</option>
    <option value="1">Masculin</option>
    <option value="2">Féminin</option>
  </select>

  <label>Centre d'encadrement</label>
  <select name="structure_id" required>
    <option value="">-- Choisir --</option>
    <?php foreach ($centres as $centre): ?>
      <option value="<?= $centre['id'] ?>"><?= htmlspecialchars($centre['nom']) ?></option>
    <?php endforeach; ?>
  </select>

  <button type="submit">Ajouter un enfant</button>
</form>
