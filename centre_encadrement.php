<?php
session_start();
// Vous pouvez ici vérifier que l'utilisateur est bien admin:
// if ($_SESSION['role'] !== 'admin') { header('Location: login.php'); exit; }
?>

<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <title>Dashboard Administrateur - Centres d'encadrement</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0; padding: 0;
      background: #f0f2f5;
      color: #333;
    }
    header {
      background: #007bff;
      color: white;
      padding: 15px 30px;
      font-size: 1.4em;
    }
    nav {
      background: #0056b3;
      width: 220px;
      height: 100vh;
      position: fixed;
      top: 60px;
      left: 0;
      padding-top: 20px;
    }
    nav a {
      display: block;
      color: white;
      text-decoration: none;
      padding: 15px 20px;
      border-left: 5px solid transparent;
      margin-bottom: 5px;
      transition: background 0.3s, border-left 0.3s;
    }
    nav a:hover, nav a.active {
      background: #004494;
      border-left: 5px solid #ffc107;
    }
    main {
      margin-left: 240px;
      padding: 20px;
    }
    h2 {
      margin-top: 0;
      color: #007bff;
    }
    form {
      background: white;
      padding: 20px;
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      max-width: 600px;
    }
    label {
      display: block;
      margin-top: 10px;
      font-weight: bold;
    }
    input[type="text"], input[type="email"], input[type="number"], select, textarea {
      width: 100%;
      padding: 8px;
      margin-top: 5px;
      border: 1px solid #ccc;
      border-radius: 4px;
      box-sizing: border-box;
      font-size: 1em;
    }
    button {
      margin-top: 20px;
      background: #007bff;
      color: white;
      padding: 10px 15px;
      font-size: 1em;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background: #0056b3;
    }
    .section-content {
      margin-top: 20px;
    }
  </style>
</head>
<body>

<header>
  Dashboard Administrateur - Centres d'encadrement
</header>

<nav>
  <a href="?page=centres" class="<?= ($_GET['page'] ?? '') === 'centres' ? 'active' : '' ?>">Gestion des Centres</a>
  <a href="?page=enfants" class="<?= ($_GET['page'] ?? '') === 'enfants' ? 'active' : '' ?>">Enregistrer Enfants</a>
  <a href="?page=parrainages" class="<?= ($_GET['page'] ?? '') === 'parrainages' ? 'active' : '' ?>">Gestion Parrainages</a>
  <a href="?page=projets" class="<?= ($_GET['page'] ?? '') === 'projets' ? 'active' : '' ?>">Gestion Projets</a>
</nav>

<main>

<?php
$page = $_GET['page'] ?? 'centres';

switch ($page) {
  case 'centres':
    include 'admin_centres.php';
    break;
  case 'enfants':
    include 'admin_enfants.php';
    break;
  case 'parrainages':
    include 'admin_parrainages.php';
    break;
  case 'projets':
    include 'gestion_projet.php';
    break;
  default:
    echo "<h2>Page non trouvée</h2>";
}
?>

</main>

</body>
</html>
