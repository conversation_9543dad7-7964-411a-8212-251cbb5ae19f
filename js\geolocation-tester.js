/**
 * Testeur Système Géolocalisation - Umwana Voice
 * Tests automatisés pour valider tous les automatismes
 */

class GeolocationTester {
    constructor() {
        this.tests = [];
        this.results = [];
        this.isRunning = false;
        this.currentTest = null;
        
        console.log('🧪 GeolocationTester initialisé');
        this.setupTests();
    }

    /**
     * Configuration des tests
     */
    setupTests() {
        this.tests = [
            {
                id: 'auto-detection',
                name: 'Auto-détection au chargement',
                description: 'Vérifier que la géolocalisation se déclenche automatiquement',
                category: 'automatisme',
                test: () => this.testAutoDetection()
            },
            {
                id: 'login-detection',
                name: 'Auto-détection à la connexion',
                description: 'Vérifier le déclenchement après connexion',
                category: 'automatisme',
                test: () => this.testLoginDetection()
            },
            {
                id: 'continuous-tracking',
                name: 'Surveillance continue',
                description: 'Vérifier le fonctionnement du tracking continu',
                category: 'tracking',
                test: () => this.testContinuousTracking()
            },
            {
                id: 'battery-optimization',
                name: 'Optimisation batterie',
                description: 'Vérifier l\'adaptation selon le niveau de batterie',
                category: 'optimisation',
                test: () => this.testBatteryOptimization()
            },
            {
                id: 'performance-cache',
                name: 'Cache de performance',
                description: 'Vérifier le fonctionnement du cache',
                category: 'performance',
                test: () => this.testPerformanceCache()
            },
            {
                id: 'preferences-system',
                name: 'Système de préférences',
                description: 'Vérifier la sauvegarde et application des préférences',
                category: 'interface',
                test: () => this.testPreferencesSystem()
            },
            {
                id: 'error-handling',
                name: 'Gestion d\'erreurs',
                description: 'Vérifier la robustesse face aux erreurs',
                category: 'robustesse',
                test: () => this.testErrorHandling()
            },
            {
                id: 'fallback-systems',
                name: 'Systèmes de fallback',
                description: 'Vérifier les alternatives en cas d\'échec',
                category: 'robustesse',
                test: () => this.testFallbackSystems()
            }
        ];
    }

    /**
     * Lancer tous les tests
     */
    async runAllTests() {
        if (this.isRunning) {
            console.log('⚠️ Tests déjà en cours');
            return;
        }

        console.log('🚀 Démarrage des tests automatisés...');
        this.isRunning = true;
        this.results = [];
        
        this.showTestInterface();
        
        for (const test of this.tests) {
            await this.runSingleTest(test);
            await this.delay(1000); // Pause entre les tests
        }
        
        this.isRunning = false;
        this.showTestResults();
    }

    /**
     * Lancer un test spécifique
     */
    async runSingleTest(test) {
        this.currentTest = test;
        this.updateTestInterface(`🧪 Test: ${test.name}`);
        
        const startTime = Date.now();
        let result = {
            id: test.id,
            name: test.name,
            category: test.category,
            status: 'running',
            startTime: startTime,
            duration: 0,
            details: [],
            errors: []
        };

        try {
            console.log(`🧪 Démarrage test: ${test.name}`);
            
            const testResult = await test.test();
            
            result.status = testResult.success ? 'passed' : 'failed';
            result.details = testResult.details || [];
            result.errors = testResult.errors || [];
            result.duration = Date.now() - startTime;
            
            console.log(`${testResult.success ? '✅' : '❌'} Test ${test.name}: ${result.status}`);
            
        } catch (error) {
            result.status = 'error';
            result.errors = [error.message];
            result.duration = Date.now() - startTime;
            
            console.error(`💥 Erreur test ${test.name}:`, error);
        }
        
        this.results.push(result);
        this.updateTestInterface(`${this.getStatusIcon(result.status)} ${test.name}`);
    }

    /**
     * Test: Auto-détection au chargement
     */
    async testAutoDetection() {
        const details = [];
        const errors = [];
        
        // Vérifier que AutoLocationManager existe
        if (!window.autoLocationManager) {
            errors.push('AutoLocationManager non disponible');
            return { success: false, details, errors };
        }
        
        details.push('AutoLocationManager disponible');
        
        // Vérifier les paramètres d'auto-détection
        const settings = window.autoLocationManager.settings;
        if (settings.enableAutoDetection) {
            details.push('Auto-détection activée dans les paramètres');
        } else {
            errors.push('Auto-détection désactivée');
        }
        
        // Simuler un déclenchement
        try {
            await window.autoLocationManager.startImmediateDetection();
            details.push('Déclenchement immédiat réussi');
        } catch (error) {
            errors.push(`Échec déclenchement: ${error.message}`);
        }
        
        return { success: errors.length === 0, details, errors };
    }

    /**
     * Test: Auto-détection à la connexion
     */
    async testLoginDetection() {
        const details = [];
        const errors = [];
        
        // Vérifier que le système de détection de connexion existe
        if (!window.loginGeolocationTrigger) {
            errors.push('LoginGeolocationTrigger non disponible');
            return { success: false, details, errors };
        }
        
        details.push('LoginGeolocationTrigger disponible');
        
        // Simuler une connexion
        try {
            sessionStorage.setItem('umwana_just_logged_in', 'true');
            sessionStorage.setItem('umwana_login_timestamp', Date.now().toString());
            
            await window.loginGeolocationTrigger.triggerPostLoginGeolocation();
            details.push('Simulation connexion réussie');
            
            // Nettoyer
            sessionStorage.removeItem('umwana_just_logged_in');
            sessionStorage.removeItem('umwana_login_timestamp');
            
        } catch (error) {
            errors.push(`Échec simulation connexion: ${error.message}`);
        }
        
        return { success: errors.length === 0, details, errors };
    }

    /**
     * Test: Surveillance continue
     */
    async testContinuousTracking() {
        const details = [];
        const errors = [];
        
        if (!window.continuousTracking) {
            errors.push('ContinuousTracking non disponible');
            return { success: false, details, errors };
        }
        
        details.push('ContinuousTracking disponible');
        
        // Tester le démarrage/arrêt
        try {
            window.continuousTracking.start();
            details.push('Démarrage surveillance réussi');
            
            await this.delay(2000);
            
            if (window.continuousTracking.isActive) {
                details.push('Surveillance active confirmée');
            } else {
                errors.push('Surveillance non active après démarrage');
            }
            
            window.continuousTracking.stop();
            details.push('Arrêt surveillance réussi');
            
        } catch (error) {
            errors.push(`Erreur surveillance: ${error.message}`);
        }
        
        return { success: errors.length === 0, details, errors };
    }

    /**
     * Test: Optimisation batterie
     */
    async testBatteryOptimization() {
        const details = [];
        const errors = [];
        
        if (!window.continuousTracking) {
            errors.push('ContinuousTracking non disponible pour test batterie');
            return { success: false, details, errors };
        }
        
        // Simuler différents niveaux de batterie
        const originalLevel = window.continuousTracking.batteryLevel;
        
        try {
            // Test batterie faible
            window.continuousTracking.batteryLevel = 0.10;
            window.continuousTracking.optimizeForBattery();
            
            if (window.continuousTracking.adaptiveSettings.interval > 180000) {
                details.push('Optimisation batterie faible OK');
            } else {
                errors.push('Optimisation batterie faible insuffisante');
            }
            
            // Test batterie normale
            window.continuousTracking.batteryLevel = 0.80;
            window.continuousTracking.optimizeForBattery();
            
            if (window.continuousTracking.adaptiveSettings.interval <= 120000) {
                details.push('Optimisation batterie normale OK');
            } else {
                errors.push('Optimisation batterie normale insuffisante');
            }
            
            // Restaurer
            window.continuousTracking.batteryLevel = originalLevel;
            
        } catch (error) {
            errors.push(`Erreur test batterie: ${error.message}`);
        }
        
        return { success: errors.length === 0, details, errors };
    }

    /**
     * Test: Cache de performance
     */
    async testPerformanceCache() {
        const details = [];
        const errors = [];
        
        if (!window.performanceOptimizer) {
            errors.push('PerformanceOptimizer non disponible');
            return { success: false, details, errors };
        }
        
        details.push('PerformanceOptimizer disponible');
        
        try {
            // Test cache de position
            const testPosition = { latitude: 45.5017, longitude: -73.5673, accuracy: 10 };
            window.performanceOptimizer.cachePosition(testPosition);
            
            const cached = window.performanceOptimizer.getCachedPosition(45.5017, -73.5673);
            if (cached) {
                details.push('Cache position fonctionne');
            } else {
                errors.push('Cache position défaillant');
            }
            
            // Test calcul de distance optimisé
            const distance = window.performanceOptimizer.calculateOptimizedDistance(
                45.5017, -73.5673, 45.5087, -73.5540
            );
            
            if (distance > 0 && distance < 10) {
                details.push('Calcul distance optimisé OK');
            } else {
                errors.push('Calcul distance optimisé incorrect');
            }
            
            // Vérifier les statistiques
            const stats = window.performanceOptimizer.getStats();
            details.push(`Cache hits: ${stats.cacheHits}, misses: ${stats.cacheMisses}`);
            
        } catch (error) {
            errors.push(`Erreur test cache: ${error.message}`);
        }
        
        return { success: errors.length === 0, details, errors };
    }

    /**
     * Test: Système de préférences
     */
    async testPreferencesSystem() {
        const details = [];
        const errors = [];
        
        if (!window.geolocationPreferences) {
            errors.push('GeolocationPreferences non disponible');
            return { success: false, details, errors };
        }
        
        details.push('GeolocationPreferences disponible');
        
        try {
            // Sauvegarder les préférences actuelles
            const originalPrefs = window.geolocationPreferences.getPreferences();
            
            // Modifier une préférence
            window.geolocationPreferences.updatePreference('enableAutoDetection', false);
            window.geolocationPreferences.savePreferences();
            
            // Recharger et vérifier
            const newPrefs = window.geolocationPreferences.loadPreferences();
            if (newPrefs.enableAutoDetection === false) {
                details.push('Sauvegarde préférences OK');
            } else {
                errors.push('Sauvegarde préférences échouée');
            }
            
            // Restaurer
            Object.keys(originalPrefs).forEach(key => {
                window.geolocationPreferences.updatePreference(key, originalPrefs[key]);
            });
            window.geolocationPreferences.savePreferences();
            
        } catch (error) {
            errors.push(`Erreur test préférences: ${error.message}`);
        }
        
        return { success: errors.length === 0, details, errors };
    }

    /**
     * Test: Gestion d'erreurs
     */
    async testErrorHandling() {
        const details = [];
        const errors = [];
        
        try {
            // Simuler une erreur de géolocalisation
            const mockError = { code: 1, message: 'Permission denied' };
            
            if (window.autoLocationManager && window.autoLocationManager.handleLocationError) {
                window.autoLocationManager.handleLocationError(mockError, { silent: true });
                details.push('Gestion erreur permission OK');
            }
            
            // Test timeout
            const timeoutError = { code: 3, message: 'Timeout' };
            if (window.autoLocationManager) {
                window.autoLocationManager.handleLocationError(timeoutError, { silent: true });
                details.push('Gestion erreur timeout OK');
            }
            
        } catch (error) {
            errors.push(`Erreur test gestion erreurs: ${error.message}`);
        }
        
        return { success: errors.length === 0, details, errors };
    }

    /**
     * Test: Systèmes de fallback
     */
    async testFallbackSystems() {
        const details = [];
        const errors = [];
        
        // Vérifier que les fallbacks sont disponibles
        if (window.autoLocationManager && window.autoLocationManager.settings.enableSmartFallbacks) {
            details.push('Fallbacks intelligents activés');
        } else {
            errors.push('Fallbacks intelligents non disponibles');
        }
        
        // Test fallback IP
        try {
            // Simuler un fallback (sans vraiment l'exécuter)
            details.push('Simulation fallback IP disponible');
        } catch (error) {
            errors.push(`Erreur test fallback: ${error.message}`);
        }
        
        return { success: errors.length === 0, details, errors };
    }

    /**
     * Afficher l'interface de test
     */
    showTestInterface() {
        const testInterface = document.createElement('div');
        testInterface.id = 'geolocation-test-interface';
        testInterface.innerHTML = `
            <div class="fixed top-4 right-4 bg-white dark:bg-gray-800 rounded-lg shadow-xl p-4 z-50 max-w-md">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white">🧪 Tests Géolocalisation</h3>
                    <button onclick="this.parentElement.parentElement.parentElement.remove()" 
                            class="text-gray-500 hover:text-gray-700">×</button>
                </div>
                <div id="test-progress" class="text-sm text-gray-600 dark:text-gray-400">
                    Initialisation...
                </div>
                <div class="mt-2">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="test-progress-bar" class="bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(testInterface);
    }

    /**
     * Mettre à jour l'interface de test
     */
    updateTestInterface(message) {
        const progressElement = document.getElementById('test-progress');
        const progressBar = document.getElementById('test-progress-bar');
        
        if (progressElement) {
            progressElement.textContent = message;
        }
        
        if (progressBar) {
            const progress = (this.results.length / this.tests.length) * 100;
            progressBar.style.width = `${progress}%`;
        }
    }

    /**
     * Afficher les résultats des tests
     */
    showTestResults() {
        const passed = this.results.filter(r => r.status === 'passed').length;
        const failed = this.results.filter(r => r.status === 'failed').length;
        const errors = this.results.filter(r => r.status === 'error').length;
        
        console.log('📊 Résultats des tests:');
        console.log(`   ✅ Réussis: ${passed}`);
        console.log(`   ❌ Échoués: ${failed}`);
        console.log(`   💥 Erreurs: ${errors}`);
        
        // Afficher les détails
        this.results.forEach(result => {
            console.log(`${this.getStatusIcon(result.status)} ${result.name} (${result.duration}ms)`);
            if (result.errors.length > 0) {
                result.errors.forEach(error => console.log(`     ⚠️ ${error}`));
            }
        });
        
        // Mettre à jour l'interface
        this.updateTestInterface(`✅ ${passed} réussis, ❌ ${failed} échoués, 💥 ${errors} erreurs`);
    }

    /**
     * Obtenir l'icône de statut
     */
    getStatusIcon(status) {
        switch (status) {
            case 'passed': return '✅';
            case 'failed': return '❌';
            case 'error': return '💥';
            case 'running': return '🔄';
            default: return '⏳';
        }
    }

    /**
     * Délai utilitaire
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Obtenir les résultats
     */
    getResults() {
        return [...this.results];
    }
}

// Instance globale
window.geolocationTester = new GeolocationTester();

// Fonction utilitaire pour lancer les tests
window.runGeolocationTests = () => {
    window.geolocationTester.runAllTests();
};

console.log('✅ GeolocationTester prêt - Utilisez runGeolocationTests() pour lancer les tests');
