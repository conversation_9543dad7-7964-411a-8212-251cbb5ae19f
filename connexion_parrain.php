<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Connexion - UmwanaVoice</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Poppins', sans-serif;
      background: #f4f6fb;
      margin: 0;
      padding: 0;
    }

    .container {
      max-width: 400px;
      margin: 80px auto;
      background: white;
      padding: 40px;
      border-radius: 12px;
      box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }

    h2 {
      text-align: center;
      color: #1e3d59;
      margin-bottom: 30px;
    }

    label {
      font-weight: 600;
      display: block;
      margin-bottom: 8px;
    }

    input[type="email"],
    input[type="password"] {
      width: 100%;
      padding: 12px;
      margin-bottom: 20px;
      border-radius: 8px;
      border: 1px solid #ccc;
      font-size: 15px;
    }

    .btn {
      width: 100%;
      padding: 12px;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      cursor: pointer;
      margin-bottom: 15px;
      transition: background-color 0.3s;
    }

    .btn-connect {
      background-color: #1e3d59;
      color: white;
    }

    .btn-connect:hover {
      background-color: #2a4f70;
    }

    .btn-inscrire {
      background-color: #f7b731;
      color: white;
    }

    .btn-inscrire:hover {
      background-color: #e0a800;
    }

    .footer-link {
      text-align: center;
      margin-top: 10px;
    }

    .footer-link a {
      color: #1e3d59;
      font-weight: 500;
      text-decoration: none;
    }

    .footer-link a:hover {
      text-decoration: underline;
    }

    .info-message {
      text-align: center;
      margin: 15px 0;
      color: #333;
      font-size: 14px;
    }
  </style>
</head>
<body>

<div class="container">
  <h2>Connexion Parrain</h2>
  <form action="traitement_connexionparrain.php" method="POST">
    <label for="email">Adresse Email</label>
    <input type="email" id="email" name="email" required>

    <label for="mot_de_passe">Mot de passe</label>
    <input type="password" id="mot_de_passe" name="mot_de_passe" required>

    <button type="submit" class="btn btn-connect">Se connecter</button>
  </form>

  <!-- Message d'information -->
  <p class="info-message">
    Vous n'avez pas encore de compte ? Inscrivez-vous ci-dessous :
  </p>

  <form action="ajouter_parrain.php" method="get">
    <button type="submit" class="btn btn-inscrire">S'inscrire</button>
  </form>

  <div class="footer-link">
    <a href="#">Mot de passe oublié ?</a>
  </div>
</div>

</body>
</html>
