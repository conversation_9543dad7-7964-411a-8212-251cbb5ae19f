/**
 * Optimiseur de Performance Géolocalisation - Umwana Voice
 * Cache intelligent et optimisation des calculs
 */

class PerformanceOptimizer {
    constructor() {
        this.cache = {
            positions: new Map(),
            distances: new Map(),
            structures: new Map(),
            geocoding: new Map()
        };
        
        this.stats = {
            cacheHits: 0,
            cacheMisses: 0,
            calculationsOptimized: 0,
            memoryUsage: 0
        };
        
        this.config = {
            maxCacheSize: 1000,
            cacheExpiry: 300000,        // 5 minutes
            distancePrecision: 0.001,   // 1m de précision
            batchSize: 50,              // Traitement par lots
            enablePreloading: true,
            enableCompression: true
        };
        
        console.log('⚡ PerformanceOptimizer initialisé');
        this.init();
    }

    /**
     * Initialisation
     */
    init() {
        this.setupCacheCleanup();
        this.setupMemoryMonitoring();
        this.preloadCommonData();
        
        // Écouter les événements de position
        window.addEventListener('continuousPositionUpdate', (event) => {
            this.cachePosition(event.detail.position);
        });
    }

    /**
     * Cache intelligent de position
     */
    cachePosition(position, key = null) {
        const cacheKey = key || this.generatePositionKey(position);
        const cacheEntry = {
            position: position,
            timestamp: Date.now(),
            accuracy: position.accuracy,
            source: 'gps'
        };
        
        this.cache.positions.set(cacheKey, cacheEntry);
        this.cleanupCache('positions');
        
        console.log(`💾 Position mise en cache: ${cacheKey}`);
    }

    /**
     * Récupérer une position du cache
     */
    getCachedPosition(lat, lng, maxAge = this.config.cacheExpiry) {
        const key = this.generatePositionKey({ latitude: lat, longitude: lng });
        const cached = this.cache.positions.get(key);
        
        if (cached && (Date.now() - cached.timestamp) < maxAge) {
            this.stats.cacheHits++;
            console.log(`✅ Cache hit position: ${key}`);
            return cached.position;
        }
        
        this.stats.cacheMisses++;
        return null;
    }

    /**
     * Cache optimisé des distances
     */
    cacheDistance(lat1, lng1, lat2, lng2, distance) {
        const key = this.generateDistanceKey(lat1, lng1, lat2, lng2);
        const cacheEntry = {
            distance: distance,
            timestamp: Date.now(),
            precision: this.config.distancePrecision
        };
        
        this.cache.distances.set(key, cacheEntry);
        this.cleanupCache('distances');
    }

    /**
     * Calcul de distance optimisé avec cache
     */
    calculateOptimizedDistance(lat1, lng1, lat2, lng2) {
        // Vérifier le cache d'abord
        const cacheKey = this.generateDistanceKey(lat1, lng1, lat2, lng2);
        const cached = this.cache.distances.get(cacheKey);
        
        if (cached && (Date.now() - cached.timestamp) < this.config.cacheExpiry) {
            this.stats.cacheHits++;
            return cached.distance;
        }
        
        // Calcul optimisé avec approximation rapide pour les courtes distances
        const distance = this.fastDistanceCalculation(lat1, lng1, lat2, lng2);
        
        // Mettre en cache
        this.cacheDistance(lat1, lng1, lat2, lng2, distance);
        this.stats.calculationsOptimized++;
        this.stats.cacheMisses++;
        
        return distance;
    }

    /**
     * Calcul de distance rapide pour courtes distances
     */
    fastDistanceCalculation(lat1, lng1, lat2, lng2) {
        const dLat = lat2 - lat1;
        const dLng = lng2 - lng1;
        
        // Pour les courtes distances, approximation linéaire plus rapide
        if (Math.abs(dLat) < 0.01 && Math.abs(dLng) < 0.01) {
            const avgLat = (lat1 + lat2) / 2;
            const latDistance = dLat * 111.32; // km par degré de latitude
            const lngDistance = dLng * 111.32 * Math.cos(avgLat * Math.PI / 180);
            return Math.sqrt(latDistance * latDistance + lngDistance * lngDistance);
        }
        
        // Formule de Haversine pour les longues distances
        return this.haversineDistance(lat1, lng1, lat2, lng2);
    }

    /**
     * Formule de Haversine optimisée
     */
    haversineDistance(lat1, lng1, lat2, lng2) {
        const R = 6371; // Rayon de la Terre en km
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLng = (lng2 - lng1) * Math.PI / 180;
        
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                  Math.sin(dLng/2) * Math.sin(dLng/2);
        
        return R * 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    }

    /**
     * Cache des structures avec indexation spatiale
     */
    cacheStructures(structures, region = 'default') {
        const cacheEntry = {
            structures: structures,
            timestamp: Date.now(),
            region: region,
            spatialIndex: this.buildSpatialIndex(structures)
        };
        
        this.cache.structures.set(region, cacheEntry);
        console.log(`🏢 ${structures.length} structures mises en cache pour région: ${region}`);
    }

    /**
     * Construire un index spatial pour les structures
     */
    buildSpatialIndex(structures) {
        const index = new Map();
        const gridSize = 0.01; // ~1km de grille
        
        structures.forEach(structure => {
            if (structure.latitude && structure.longitude) {
                const gridX = Math.floor(structure.latitude / gridSize);
                const gridY = Math.floor(structure.longitude / gridSize);
                const gridKey = `${gridX},${gridY}`;
                
                if (!index.has(gridKey)) {
                    index.set(gridKey, []);
                }
                index.get(gridKey).push(structure);
            }
        });
        
        return index;
    }

    /**
     * Recherche optimisée de structures proches
     */
    findNearbyStructuresOptimized(userLat, userLng, maxDistance = 10) {
        const region = 'default'; // Pourrait être dynamique selon la zone
        const cached = this.cache.structures.get(region);
        
        if (!cached || (Date.now() - cached.timestamp) > this.config.cacheExpiry) {
            console.log('⚠️ Cache structures expiré, rechargement nécessaire');
            return null;
        }
        
        // Utiliser l'index spatial pour une recherche rapide
        const gridSize = 0.01;
        const searchRadius = Math.ceil(maxDistance / 111.32 / gridSize); // Convertir km en grilles
        const userGridX = Math.floor(userLat / gridSize);
        const userGridY = Math.floor(userLng / gridSize);
        
        const nearbyStructures = [];
        
        // Chercher dans les grilles adjacentes
        for (let x = userGridX - searchRadius; x <= userGridX + searchRadius; x++) {
            for (let y = userGridY - searchRadius; y <= userGridY + searchRadius; y++) {
                const gridKey = `${x},${y}`;
                const gridStructures = cached.spatialIndex.get(gridKey);
                
                if (gridStructures) {
                    gridStructures.forEach(structure => {
                        const distance = this.calculateOptimizedDistance(
                            userLat, userLng, 
                            structure.latitude, structure.longitude
                        );
                        
                        if (distance <= maxDistance) {
                            nearbyStructures.push({
                                ...structure,
                                distance: distance
                            });
                        }
                    });
                }
            }
        }
        
        // Trier par distance
        nearbyStructures.sort((a, b) => a.distance - b.distance);
        
        this.stats.cacheHits++;
        console.log(`🎯 ${nearbyStructures.length} structures trouvées via index spatial`);
        
        return nearbyStructures;
    }

    /**
     * Préchargement des données communes
     */
    async preloadCommonData() {
        if (!this.config.enablePreloading) return;
        
        console.log('🚀 Préchargement des données communes...');
        
        try {
            // Précharger les structures les plus consultées
            // (Ceci devrait être adapté selon votre API)
            
            // Simuler le préchargement
            setTimeout(() => {
                console.log('✅ Préchargement terminé');
            }, 1000);
            
        } catch (error) {
            console.warn('⚠️ Erreur préchargement:', error);
        }
    }

    /**
     * Traitement par lots optimisé
     */
    processBatch(items, processor, batchSize = this.config.batchSize) {
        return new Promise((resolve) => {
            const results = [];
            let index = 0;
            
            const processBatch = () => {
                const batch = items.slice(index, index + batchSize);
                
                batch.forEach(item => {
                    results.push(processor(item));
                });
                
                index += batchSize;
                
                if (index < items.length) {
                    // Traitement asynchrone pour éviter de bloquer l'UI
                    setTimeout(processBatch, 0);
                } else {
                    resolve(results);
                }
            };
            
            processBatch();
        });
    }

    /**
     * Génération de clés de cache
     */
    generatePositionKey(position) {
        const lat = Math.round(position.latitude / this.config.distancePrecision) * this.config.distancePrecision;
        const lng = Math.round(position.longitude / this.config.distancePrecision) * this.config.distancePrecision;
        return `pos_${lat}_${lng}`;
    }

    generateDistanceKey(lat1, lng1, lat2, lng2) {
        // Normaliser l'ordre pour éviter les doublons (A->B = B->A)
        const points = [[lat1, lng1], [lat2, lng2]].sort();
        return `dist_${points[0][0]}_${points[0][1]}_${points[1][0]}_${points[1][1]}`;
    }

    /**
     * Nettoyage automatique du cache
     */
    setupCacheCleanup() {
        setInterval(() => {
            this.cleanupExpiredCache();
            this.optimizeMemoryUsage();
        }, 60000); // Toutes les minutes
    }

    /**
     * Nettoyer le cache expiré
     */
    cleanupExpiredCache() {
        const now = Date.now();
        let cleaned = 0;
        
        ['positions', 'distances', 'structures', 'geocoding'].forEach(cacheType => {
            const cache = this.cache[cacheType];
            
            for (const [key, entry] of cache.entries()) {
                if (now - entry.timestamp > this.config.cacheExpiry) {
                    cache.delete(key);
                    cleaned++;
                }
            }
        });
        
        if (cleaned > 0) {
            console.log(`🧹 ${cleaned} entrées de cache nettoyées`);
        }
    }

    /**
     * Nettoyer un type de cache spécifique
     */
    cleanupCache(cacheType) {
        const cache = this.cache[cacheType];
        
        if (cache.size > this.config.maxCacheSize) {
            // Supprimer les plus anciennes entrées
            const entries = Array.from(cache.entries())
                .sort((a, b) => a[1].timestamp - b[1].timestamp);
            
            const toRemove = entries.slice(0, cache.size - this.config.maxCacheSize);
            toRemove.forEach(([key]) => cache.delete(key));
            
            console.log(`🧹 ${toRemove.length} entrées supprimées du cache ${cacheType}`);
        }
    }

    /**
     * Surveillance de l'utilisation mémoire
     */
    setupMemoryMonitoring() {
        setInterval(() => {
            this.updateMemoryStats();
        }, 30000); // Toutes les 30 secondes
    }

    /**
     * Mettre à jour les statistiques mémoire
     */
    updateMemoryStats() {
        let totalSize = 0;
        
        Object.values(this.cache).forEach(cache => {
            totalSize += cache.size;
        });
        
        this.stats.memoryUsage = totalSize;
        
        // Optimiser si nécessaire
        if (totalSize > this.config.maxCacheSize * 0.8) {
            console.log('⚠️ Utilisation mémoire élevée, optimisation...');
            this.optimizeMemoryUsage();
        }
    }

    /**
     * Optimiser l'utilisation mémoire
     */
    optimizeMemoryUsage() {
        // Réduire la taille des caches si nécessaire
        Object.keys(this.cache).forEach(cacheType => {
            this.cleanupCache(cacheType);
        });
        
        // Forcer le garbage collection si disponible
        if (window.gc) {
            window.gc();
        }
    }

    /**
     * Obtenir les statistiques de performance
     */
    getStats() {
        const hitRate = this.stats.cacheHits / (this.stats.cacheHits + this.stats.cacheMisses) * 100;
        
        return {
            ...this.stats,
            hitRate: hitRate.toFixed(1) + '%',
            cacheSize: {
                positions: this.cache.positions.size,
                distances: this.cache.distances.size,
                structures: this.cache.structures.size,
                geocoding: this.cache.geocoding.size
            }
        };
    }

    /**
     * Vider tous les caches
     */
    clearAllCaches() {
        Object.values(this.cache).forEach(cache => cache.clear());
        console.log('🧹 Tous les caches vidés');
    }
}

// Instance globale
window.performanceOptimizer = new PerformanceOptimizer();

console.log('✅ PerformanceOptimizer prêt');
