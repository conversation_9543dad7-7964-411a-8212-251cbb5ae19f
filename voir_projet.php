<?php
// Connexion à la base de données
$pdo = new PDO('mysql:host=localhost;dbname=gestion_enfant;charset=utf8', 'root', '');

// Vérification de l'ID du projet
if (!isset($_GET['id'])) {
    die("ID de projet manquant.");
}

$id = $_GET['id'];

// Récupération du projet
$stmt = $pdo->prepare("SELECT * FROM projet WHERE id = ?");
$stmt->execute([$id]);
$projet = $stmt->fetch(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Détails du Projet</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f2f2f2;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h2 {
            text-align: center;
        }
        .btn {
            padding: 10px 15px;
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Détails du Projet</h2>
        <p><strong>Titre :</strong> <?php echo htmlspecialchars($projet['titre']); ?></p>
        <p><strong>Description :</strong> <?php echo htmlspecialchars($projet['description']); ?></p>
        <p><strong>Objectif Financier :</strong> <?php echo htmlspecialchars($projet['objectif_financier']); ?></p>
        <p><strong>Document :</strong> <a href="<?php echo htmlspecialchars($projet['document']); ?>" target="_blank">Voir le Document</a></p>
        <button class="btn" onclick="window.location.href='gestion_projets.php'">Retour</button>
    </div>
</body>
</html>