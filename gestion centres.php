<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Directeur de Centre - Tableau de Bord</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">

  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Poppins', sans-serif;
      background-color: #f2f4f8;
      color: #2c3e50;
    }

    header {
      background-color: #1e3d59;
      color: white;
      padding: 20px 40px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    header h1 {
      font-size: 24px;
    }

    .btn-logout {
      background-color: #e74c3c;
      border: none;
      color: white;
      padding: 10px 16px;
      border-radius: 6px;
      cursor: pointer;
      font-weight: bold;
      transition: background-color 0.3s;
    }

    .btn-logout:hover {
      background-color: #c0392b;
    }

    .container {
      padding: 40px 30px;
    }

    .title-section {
      text-align: center;
      margin-bottom: 40px;
      font-size: 26px;
      font-weight: 600;
    }

    .grid-cards {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 30px;
    }

    .card {
      background-color: white;
      width: 300px;
      border-radius: 12px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
      padding: 25px;
      text-align: center;
      transition: transform 0.3s ease;
    }

    .card:hover {
      transform: translateY(-5px);
    }

    .card h2 {
      color: #1e3d59;
      margin-bottom: 15px;
    }

    .card p {
      font-size: 14px;
      color: #555;
      margin-bottom: 20px;
    }

    .btn-acces {
      display: inline-block;
      padding: 10px 18px;
      background-color: #1e3d59;
      color: white;
      border-radius: 6px;
      font-size: 14px;
      font-weight: bold;
      transition: background-color 0.3s ease;
      text-decoration: none;
    }

    .btn-acces:hover {
      background-color: #163146;
    }

    @media screen and (max-width: 600px) {
      .grid-cards {
        flex-direction: column;
        align-items: center;
      }

      .card {
        width: 90%;
      }
    }
  </style>
</head>

<body>

<!-- HEADER -->
<header>
  <h1>Tableau de Bord - Directeur de Centre</h1>
  <button class="btn-logout" onclick="window.location.href='logout.php'">Déconnexion</button>
</header>

<!-- CONTENU -->
<div class="container">
  <div class="title-section">Gérer les Structures d'Accueil</div>

  <div class="grid-cards">

    <!-- Orphelinats -->
    <div class="card">
      <h2>Orphelinats</h2>
      <p>Consultez et gérez les orphelinats enregistrés dans le système.</p>
      <a href="orphelinat.php" class="btn-acces">Accéder à la page</a>
    </div>

    <!-- Centres d'encadrement -->
    <div class="card">
      <h2>Centres d'encadrement</h2>
      <p>Gérez les centres d'encadrement des enfants sous votre responsabilité.</p>
      <a href="dashboard_centre.php" class="btn-acces">Accéder à la page</a>
    </div>

    <!-- Familles d'adoption -->
    <div class="card">
      <h2>Familles d’adoption</h2>
      <p>Consultez et gérez les familles d’accueil et d’adoption.</p>
      <a href="familles.php" class="btn-acces">Accéder à la page</a>
    </div>

  </div>
</div>

</body>
</html>
