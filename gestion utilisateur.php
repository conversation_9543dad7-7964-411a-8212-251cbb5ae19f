<?php
require_once 'config.php';
session_start();

// Variables
$edit_mode = false;
$success_message = "";
$error_message = "";

// Récupération des rôles
$roles = $pdo->query("SELECT id, nom FROM type_utilisateur")->fetchAll();

// Création ou mise à jour
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $id = $_POST["id"] ?? null;
    $nom = trim($_POST["nom"]);
    $prenom = trim($_POST["prenom"]);
    $email = strtolower(trim($_POST["email"]));
    $telephone = trim($_POST["telephone"]);
    $type_utilisateur_id = intval($_POST["type_utilisateur_id"]);

    // Mot de passe : seulement si nouveau ou mis à jour
    if (!empty($_POST["mot_de_passe"])) {
        $mot_de_passe = password_hash($_POST["mot_de_passe"], PASSWORD_DEFAULT);
    }

    if ($id) {
        // Mise à jour
        $sql = "UPDATE utilisateur SET nom = :nom, prenom = :prenom, email = :email, telephone = :telephone, type_utilisateur_id = :type_utilisateur_id";
        if (!empty($_POST["mot_de_passe"])) {
            $sql .= ", mot_de_passe = :mot_de_passe";
        }
        $sql .= " WHERE id = :id";
        $stmt = $pdo->prepare($sql);

        $params = [
            'nom' => $nom,
            'prenom' => $prenom,
            'email' => $email,
            'telephone' => $telephone,
            'type_utilisateur_id' => $type_utilisateur_id,
            'id' => $id
        ];
        if (!empty($_POST["mot_de_passe"])) {
            $params['mot_de_passe'] = $mot_de_passe;
        }

        $stmt->execute($params);
        $success_message = "Utilisateur mis à jour avec succès.";
    } else {
        // Insertion
        $mot_de_passe = password_hash($_POST["mot_de_passe"], PASSWORD_DEFAULT);
        $sql = "INSERT INTO utilisateur (nom, prenom, email, mot_de_passe, telephone, date_creation, type_utilisateur_id) 
                VALUES (:nom, :prenom, :email, :mot_de_passe, :telephone, NOW(), :type_utilisateur_id)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            'nom' => $nom,
            'prenom' => $prenom,
            'email' => $email,
            'mot_de_passe' => $mot_de_passe,
            'telephone' => $telephone,
            'type_utilisateur_id' => $type_utilisateur_id
        ]);
        $success_message = "Nouvel utilisateur ajouté.";
    }
}

// Suppression
if (isset($_GET['delete'])) {
    $id = intval($_GET['delete']);
    $stmt = $pdo->prepare("DELETE FROM utilisateur WHERE id = :id");
    $stmt->execute(['id' => $id]);
    $success_message = "Utilisateur supprimé.";
}

// Chargement pour édition
$edit_user = null;
if (isset($_GET['edit'])) {
    $edit_mode = true;
    $id = intval($_GET['edit']);
    $stmt = $pdo->prepare("SELECT * FROM utilisateur WHERE id = :id");
    $stmt->execute(['id' => $id]);
    $edit_user = $stmt->fetch();
}

// Liste des utilisateurs
$sql = "SELECT u.*, t.nom AS role FROM utilisateur u JOIN type_utilisateur t ON u.type_utilisateur_id = t.id";
$users = $pdo->query($sql)->fetchAll();
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Gestion des utilisateurs - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
<div class="container mt-5">
    <h2 class="mb-4">Gestion des utilisateurs</h2>

    <?php if ($success_message): ?>
        <div class="alert alert-success"><?= $success_message ?></div>
    <?php elseif ($error_message): ?>
        <div class="alert alert-danger"><?= $error_message ?></div>
    <?php endif; ?>

    <!-- Formulaire -->
    <form method="POST" class="bg-white p-4 shadow rounded mb-5">
        <h5><?= $edit_mode ? "Modifier un utilisateur" : "Ajouter un nouvel utilisateur" ?></h5>
        <?php if ($edit_mode): ?>
            <input type="hidden" name="id" value="<?= $edit_user['id'] ?>">
        <?php endif; ?>

        <div class="row mb-3">
            <div class="col">
                <label for="nom" class="form-label">Nom</label>
                <input type="text" name="nom" id="nom" class="form-control" required value="<?= $edit_user['nom'] ?? '' ?>">
            </div>
            <div class="col">
                <label for="prenom" class="form-label">Prénom</label>
                <input type="text" name="prenom" id="prenom" class="form-control" required value="<?= $edit_user['prenom'] ?? '' ?>">
            </div>
        </div>
        <div class="mb-3">
            <label for="email" class="form-label">Email</label>
            <input type="email" name="email" id="email" class="form-control" required value="<?= $edit_user['email'] ?? '' ?>">
        </div>
        <div class="mb-3">
            <label for="mot_de_passe" class="form-label">
                Mot de passe <?= $edit_mode ? "(laisser vide pour ne pas changer)" : "" ?>
            </label>
            <input type="password" name="mot_de_passe" id="mot_de_passe" class="form-control" <?= $edit_mode ? "" : "required" ?>>
        </div>
        <div class="mb-3">
            <label for="telephone" class="form-label">Téléphone</label>
            <input type="text" name="telephone" id="telephone" class="form-control" value="<?= $edit_user['telephone'] ?? '' ?>">
        </div>
        <div class="mb-3">
            <label for="type_utilisateur_id" class="form-label">Rôle</label>
            <select name="type_utilisateur_id" id="type_utilisateur_id" class="form-select" required>
                <option value="">-- Sélectionner un rôle --</option>
                <?php foreach ($roles as $role): ?>
                    <option value="<?= $role['id'] ?>"
                        <?= (isset($edit_user) && $edit_user['type_utilisateur_id'] == $role['id']) ? 'selected' : '' ?>>
                        <?= htmlspecialchars($role['nom']) ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        <button type="submit" class="btn btn-primary"><?= $edit_mode ? "Mettre à jour" : "Ajouter" ?></button>
        <?php if ($edit_mode): ?>
            <a href="admin_gestion_utilisateurs.php" class="btn btn-secondary">Annuler</a>
        <?php endif; ?>
    </form>

    <!-- Liste des utilisateurs -->
    <table class="table table-bordered bg-white shadow-sm">
        <thead class="table-dark">
            <tr>
                <th>Nom</th>
                <th>Prénom</th>
                <th>Email</th>
                <th>Téléphone</th>
                <th>Rôle</th>
                <th>Date création</th>
                <th>Dernière connexion</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
        <?php foreach ($users as $user): ?>
            <tr>
                <td><?= htmlspecialchars($user['nom']) ?></td>
                <td><?= htmlspecialchars($user['prenom']) ?></td>
                <td><?= htmlspecialchars($user['email']) ?></td>
                <td><?= htmlspecialchars($user['telephone']) ?></td>
                <td><?= htmlspecialchars($user['role']) ?></td>
                <td><?= $user['date_creation'] ?></td>
                <td><?= $user['dernier_connexion'] ?? '-' ?></td>
                <td>
                    <a href="?edit=<?= $user['id'] ?>" class="btn btn-sm btn-warning">Modifier</a>
                    <a href="?delete=<?= $user['id'] ?>" class="btn btn-sm btn-danger" onclick="return confirm('Supprimer cet utilisateur ?')">Supprimer</a>
                </td>
            </tr>
        <?php endforeach ?>
        </tbody>
    </table>
</div>
</body>
</html>
