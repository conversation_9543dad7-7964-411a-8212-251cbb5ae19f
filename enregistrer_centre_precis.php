<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enregistrement Précis de Centre - Umwana Voice</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        .map-container {
            height: 400px;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #e5e7eb;
        }
        
        .precision-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1000;
            background: white;
            padding: 8px 12px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            font-size: 12px;
            font-weight: bold;
        }
        
        .precision-high { color: #10b981; }
        .precision-medium { color: #f59e0b; }
        .precision-low { color: #ef4444; }
        
        .coordinate-display {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 12px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8 max-w-4xl">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="mb-6">
                <h1 class="text-2xl font-bold text-gray-800 mb-2">
                    🎯 Enregistrement Précis de Centre
                </h1>
                <p class="text-gray-600">
                    Utilisez cette interface pour enregistrer un centre avec une géolocalisation haute précision
                </p>
            </div>

            <!-- Alertes -->
            <div id="alertContainer" class="mb-6 hidden">
                <div id="alertBox" class="p-4 rounded-lg border-l-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <span id="alertIcon" class="text-xl"></span>
                        </div>
                        <div class="ml-3">
                            <p id="alertTitle" class="text-sm font-medium"></p>
                            <p id="alertMessage" class="text-sm mt-1"></p>
                        </div>
                    </div>
                </div>
            </div>

            <form id="centreForm" class="space-y-6">
                <!-- Informations de base -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Nom du Centre *
                        </label>
                        <input type="text" name="nom" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Ex: Orphelinat Espoir">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Type de Structure *
                        </label>
                        <select name="type_structure_id" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Sélectionner le type</option>
                            <option value="1">Orphelinat</option>
                            <option value="2">ONG</option>
                            <option value="3">Centre d'Accueil</option>
                            <option value="4">Centre d'Urgence</option>
                            <option value="5">Famille d'Accueil</option>
                        </select>
                    </div>
                </div>

                <!-- Adresse -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Adresse Complète *
                    </label>
                    <input type="text" name="adresse" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="Ex: Avenue de l'Indépendance, Quartier Rohero, Bujumbura">
                </div>

                <!-- Géolocalisation -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Position GPS Précise *
                    </label>
                    
                    <!-- Boutons de géolocalisation -->
                    <div class="flex flex-wrap gap-3 mb-4">
                        <button type="button" id="btnAutoLocation" 
                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md font-medium">
                            📍 Détecter Position Automatique
                        </button>
                        <button type="button" id="btnManualLocation"
                                class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md font-medium">
                            🎯 Sélection Manuelle
                        </button>
                        <button type="button" id="btnValidateLocation"
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md font-medium">
                            ✅ Valider Position
                        </button>
                    </div>

                    <!-- Carte -->
                    <div class="relative">
                        <div id="map" class="map-container"></div>
                        <div id="precisionIndicator" class="precision-indicator hidden">
                            <span id="precisionText">Précision: --</span>
                        </div>
                    </div>

                    <!-- Affichage des coordonnées -->
                    <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Latitude</label>
                            <input type="number" name="latitude" id="latitudeInput" step="any" required readonly
                                   class="coordinate-display w-full">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Longitude</label>
                            <input type="number" name="longitude" id="longitudeInput" step="any" required readonly
                                   class="coordinate-display w-full">
                        </div>
                    </div>

                    <!-- Informations de précision -->
                    <div id="precisionInfo" class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md hidden">
                        <h4 class="font-medium text-blue-800 mb-2">Informations de Précision</h4>
                        <div class="text-sm text-blue-700 space-y-1">
                            <div>Précision: <span id="accuracyValue">--</span> mètres</div>
                            <div>Tentatives: <span id="attemptsValue">--</span></div>
                            <div>Méthode: <span id="methodValue">--</span></div>
                        </div>
                    </div>
                </div>

                <!-- Capacités -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Capacité Maximum *
                        </label>
                        <input type="number" name="capacite_max" required min="1"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Capacité Actuelle
                        </label>
                        <input type="number" name="capacite_actuelle" min="0"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <!-- Contact -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Téléphone *
                        </label>
                        <input type="tel" name="telephone" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="+257 XX XX XX XX">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Email
                        </label>
                        <input type="email" name="email"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <!-- Responsable -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Responsable *
                    </label>
                    <input type="text" name="responsable" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="Nom du responsable">
                </div>

                <!-- Bouton de soumission -->
                <div class="pt-6">
                    <button type="submit" id="submitBtn"
                            class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-4 rounded-md transition duration-200">
                        💾 Enregistrer le Centre
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="js/precise-geolocation.js"></script>
    <script>
        // Variables globales
        let map;
        let currentMarker;
        let preciseGeo;
        let selectedPosition = null;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
            initGeolocation();
            initEventListeners();
        });

        // Initialiser la carte
        function initMap() {
            map = L.map('map').setView([-3.3614, 29.3599], 13);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);
        }

        // Initialiser la géolocalisation précise
        function initGeolocation() {
            preciseGeo = new PreciseGeolocation();
        }

        // Initialiser les événements
        function initEventListeners() {
            document.getElementById('btnAutoLocation').addEventListener('click', startAutoLocation);
            document.getElementById('btnManualLocation').addEventListener('click', startManualLocation);
            document.getElementById('btnValidateLocation').addEventListener('click', validateLocation);
            document.getElementById('centreForm').addEventListener('submit', handleFormSubmit);
        }

        // Démarrer la géolocalisation automatique
        function startAutoLocation() {
            const btn = document.getElementById('btnAutoLocation');
            btn.innerHTML = '⏳ Localisation en cours...';
            btn.disabled = true;

            preciseGeo.getPreciseLocation({
                onSuccess: handleLocationSuccess,
                onError: handleLocationError,
                onProgress: handleLocationProgress
            });
        }

        // Gérer le succès de localisation
        function handleLocationSuccess(position) {
            selectedPosition = position;
            updateMapMarker(position.latitude, position.longitude, position.accuracy);
            updateCoordinateInputs(position.latitude, position.longitude);
            updatePrecisionDisplay(position);

            const btn = document.getElementById('btnAutoLocation');
            btn.innerHTML = '✅ Position Détectée';
            btn.disabled = false;

            showAlert('success', 'Position trouvée',
                `Position détectée avec une précision de ${position.accuracy.toFixed(0)}m`);
        }

        // Gérer les erreurs de localisation
        function handleLocationError(error) {
            const btn = document.getElementById('btnAutoLocation');
            btn.innerHTML = '📍 Détecter Position Automatique';
            btn.disabled = false;

            showAlert('error', 'Erreur de géolocalisation', error.message);
        }

        // Gérer le progrès de localisation
        function handleLocationProgress(progress) {
            const btn = document.getElementById('btnAutoLocation');
            btn.innerHTML = `⏳ ${progress.message}`;

            if (progress.accuracy) {
                updatePrecisionDisplay({ accuracy: progress.accuracy, attempts: progress.attempt });
            }
        }

        // Démarrer la sélection manuelle
        function startManualLocation() {
            showAlert('info', 'Sélection manuelle', 'Cliquez sur la carte pour définir la position du centre');

            map.once('click', function(e) {
                const lat = e.latlng.lat;
                const lng = e.latlng.lng;

                // Vérifier si la position est au Burundi
                if (!isValidBurundiCoordinate(lat, lng)) {
                    showAlert('error', 'Position invalide',
                        'La position sélectionnée est en dehors du Burundi. Veuillez sélectionner une position valide.');
                    return;
                }

                selectedPosition = {
                    latitude: lat,
                    longitude: lng,
                    accuracy: 10, // Précision estimée pour sélection manuelle
                    method: 'manual'
                };

                updateMapMarker(lat, lng, 10);
                updateCoordinateInputs(lat, lng);
                updatePrecisionDisplay(selectedPosition);

                showAlert('success', 'Position définie', 'Position manuelle définie avec succès');
            });
        }

        // Valider la position
        function validateLocation() {
            if (!selectedPosition) {
                showAlert('error', 'Aucune position', 'Veuillez d\'abord définir une position GPS');
                return;
            }

            const lat = selectedPosition.latitude;
            const lng = selectedPosition.longitude;

            if (!isValidBurundiCoordinate(lat, lng)) {
                showAlert('error', 'Position invalide', 'La position n\'est pas valide pour le Burundi');
                return;
            }

            showAlert('success', 'Position validée',
                `Position validée: ${lat.toFixed(6)}, ${lng.toFixed(6)}`);
        }

        // Mettre à jour le marqueur sur la carte
        function updateMapMarker(lat, lng, accuracy) {
            if (currentMarker) {
                map.removeLayer(currentMarker);
            }

            currentMarker = L.marker([lat, lng]).addTo(map);
            currentMarker.bindPopup(`Position: ${lat.toFixed(6)}, ${lng.toFixed(6)}<br>Précision: ${accuracy.toFixed(0)}m`);

            // Ajouter un cercle de précision
            if (accuracy < 100) {
                L.circle([lat, lng], {
                    radius: accuracy,
                    color: '#3b82f6',
                    fillColor: '#3b82f6',
                    fillOpacity: 0.1,
                    weight: 1
                }).addTo(map);
            }

            map.setView([lat, lng], 16);
        }

        // Mettre à jour les champs de coordonnées
        function updateCoordinateInputs(lat, lng) {
            document.getElementById('latitudeInput').value = lat.toFixed(8);
            document.getElementById('longitudeInput').value = lng.toFixed(8);
        }

        // Mettre à jour l'affichage de précision
        function updatePrecisionDisplay(position) {
            const indicator = document.getElementById('precisionIndicator');
            const precisionText = document.getElementById('precisionText');
            const precisionInfo = document.getElementById('precisionInfo');

            indicator.classList.remove('hidden');
            precisionInfo.classList.remove('hidden');

            const accuracy = position.accuracy || 0;
            let precisionClass = 'precision-low';
            let precisionLabel = 'Faible';

            if (accuracy <= 10) {
                precisionClass = 'precision-high';
                precisionLabel = 'Excellente';
            } else if (accuracy <= 50) {
                precisionClass = 'precision-medium';
                precisionLabel = 'Bonne';
            }

            precisionText.textContent = `Précision: ${precisionLabel}`;
            precisionText.className = precisionClass;

            document.getElementById('accuracyValue').textContent = accuracy.toFixed(0);
            document.getElementById('attemptsValue').textContent = position.attempts || 1;
            document.getElementById('methodValue').textContent = position.method || 'GPS';
        }

        // Vérifier si les coordonnées sont valides pour le Burundi
        function isValidBurundiCoordinate(lat, lng) {
            return lat >= -4.5 && lat <= -2.3 && lng >= 28.9 && lng <= 30.9;
        }

        // Gérer la soumission du formulaire
        function handleFormSubmit(e) {
            e.preventDefault();

            if (!selectedPosition) {
                showAlert('error', 'Position GPS requise', 'Veuillez définir la position GPS du centre');
                return;
            }

            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());

            // Ajouter les informations de géolocalisation
            data.latitude = selectedPosition.latitude;
            data.longitude = selectedPosition.longitude;
            data.gps_accuracy = selectedPosition.accuracy;
            data.location_method = selectedPosition.method || 'gps';

            submitCentre(data);
        }

        // Soumettre les données du centre
        async function submitCentre(data) {
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.innerHTML = '⏳ Enregistrement en cours...';
            submitBtn.disabled = true;

            try {
                const response = await fetch('api/add_structure.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams(data)
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('success', 'Centre enregistré', 'Le centre a été enregistré avec succès');
                    document.getElementById('centreForm').reset();
                    selectedPosition = null;
                    if (currentMarker) {
                        map.removeLayer(currentMarker);
                    }
                } else {
                    showAlert('error', 'Erreur d\'enregistrement', result.message);
                }
            } catch (error) {
                showAlert('error', 'Erreur réseau', 'Impossible de contacter le serveur');
            }

            submitBtn.innerHTML = '💾 Enregistrer le Centre';
            submitBtn.disabled = false;
        }

        // Afficher une alerte
        function showAlert(type, title, message) {
            const container = document.getElementById('alertContainer');
            const box = document.getElementById('alertBox');
            const icon = document.getElementById('alertIcon');
            const titleEl = document.getElementById('alertTitle');
            const messageEl = document.getElementById('alertMessage');

            // Configuration selon le type
            const configs = {
                success: {
                    icon: '✅',
                    classes: 'bg-green-50 border-green-400 text-green-800'
                },
                error: {
                    icon: '❌',
                    classes: 'bg-red-50 border-red-400 text-red-800'
                },
                info: {
                    icon: 'ℹ️',
                    classes: 'bg-blue-50 border-blue-400 text-blue-800'
                }
            };

            const config = configs[type] || configs.info;

            icon.textContent = config.icon;
            titleEl.textContent = title;
            messageEl.textContent = message;
            box.className = `p-4 rounded-lg border-l-4 ${config.classes}`;

            container.classList.remove('hidden');

            // Masquer automatiquement après 5 secondes
            setTimeout(() => {
                container.classList.add('hidden');
            }, 5000);
        }
    </script>
</body>
</html>
