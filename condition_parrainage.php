<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Conditions de Parrainage</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

  <style>
    body {
      font-family: 'Poppins', sans-serif;
      background-color: #f4f6fb;
      margin: 0;
      padding: 0;
      color: #2c3e50;
    }

    .container {
      max-width: 960px;
      margin: 40px auto;
      padding: 40px 30px;
      background-color: #ffffff;
      border-radius: 16px;
      box-shadow: 0 12px 30px rgba(0, 0, 0, 0.08);
    }

    .titre {
      text-align: center;
      margin-bottom: 30px;
    }

    .titre h1 {
      font-size: 34px;
      color: #1e3d59;
      margin-bottom: 10px;
    }

    .description {
      font-size: 17px;
      line-height: 1.8;
      text-align: justify;
      margin-bottom: 50px;
      padding: 0 10px;
    }

    .prerequis {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-around;
      gap: 30px;
      margin-bottom: 50px;
    }

    .item {
      width: 260px;
      text-align: center;
    }

    .icon-circle {
      width: 80px;
      height: 80px;
      margin: auto;
      border-radius: 50%;
      background-color: #f7b731;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 15px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      transition: transform 0.3s;
    }

    .icon-circle i {
      font-size: 32px;
      color: white;
    }

    .item:hover .icon-circle {
      transform: scale(1.1);
      background-color: #e0a800;
    }

    .item p {
      font-size: 16px;
      font-weight: 500;
      margin-top: 5px;
    }

    .btn-center {
      text-align: center;
    }

    .btn-parrainer {
      background-color: #1e3d59;
      color: white;
      padding: 14px 36px;
      font-size: 17px;
      font-weight: 600;
      text-decoration: none;
      border-radius: 10px;
      box-shadow: 0 4px 10px rgba(0,0,0,0.1);
      transition: background-color 0.3s ease;
    }

    .btn-parrainer:hover {
      background-color: #2a4f70;
    }

    @media screen and (max-width: 768px) {
      .prerequis {
        flex-direction: column;
        align-items: center;
      }

      .item {
        width: 90%;
      }
    }
  </style>
</head>
<body>

<div class="container">
  <div class="titre">
    <h1>Conditions pour devenir Parrain ou Marraine</h1>
  </div>

  <div class="description">
    <p>
      Afin d’assurer un accompagnement stable et sécurisé à chaque enfant parrainé, nous demandons aux futurs parrains ou marraines de remplir certains critères.
      Ces conditions nous permettent de mieux vous connaître et de garantir que les enfants bénéficient d’un environnement de soutien fiable.
    </p>
    <p>
      Vous devrez fournir des documents justificatifs de votre identité, de votre stabilité financière et de votre lieu de résidence. Une fois ces documents validés, vous pourrez accéder à l’espace de parrainage et choisir un enfant à accompagner.
    </p>
  </div>

  <div class="prerequis">
    <div class="item">
      <div class="icon-circle" style="background-color: #28a745;">
        <i class="fas fa-file-invoice-dollar"></i>
      </div>
      <p>Situation bancaire stable (1 an)</p>
    </div>

    <div class="item">
      <div class="icon-circle" style="background-color: #007bff;">
        <i class="fas fa-id-card"></i>
      </div>
      <p>CNI ou Passeport valide</p>
    </div>

    <div class="item">
      <div class="icon-circle" style="background-color: #e83e8c;">
        <i class="fas fa-map-marker-alt"></i>
      </div>
      <p>Attestation de résidence</p>
    </div>
  </div>

  <div class="btn-center">
    <a href="connexion_parrain.php" class="btn-parrainer">Parrainer</a>
  </div>
</div>

</body>
</html>
